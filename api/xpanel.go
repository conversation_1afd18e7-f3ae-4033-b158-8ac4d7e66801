package api

import (
	"fmt"
	"net/http"
	"time"

	"billing_service/model"
	"billing_service/util"

	fiber "github.com/gofiber/fiber/v3"
	null "github.com/guregu/null/v6"
)

func (h *handler) XpanelGetClientPaymePayments(c fiber.Ctx) error {
	orderId := util.ParseInt(c.Params("order_id"))
	if orderId == 0 {
		return h.badRequestResponse(c, "incorrect order_id")
	}

	resp, err := h.app.XpanelGetClientPaymePayments(c, orderId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	resp.Status = "success"

	return c.JSON(resp)
}

func (h *handler) XpanelGetDriverA2CPayments(c fiber.Ctx) error {
	orderId := util.ParseInt(c.Query("order_id"))
	if orderId == 0 {
		return h.badRequestResponse(c, "incorrect order_id")
	}

	resp, err := h.repo.XpanelGetDriverA2CPayments(c, orderId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) XpanelGetBillingPayments(c fiber.Ctx) error {
	var req model.XpanelGetBillingPaymentsRequest

	req.Page = util.ParseInt(c.Query("page"))
	req.Limit = util.ParseInt(c.Query("limit"))
	req.AccountNumber = c.Query("account_number")
	req.Type = c.Query("type")
	req.OpType = util.ParseInt(c.Query("op_type"))
	req.OrderID = c.Query("order_id")
	req.TransactionID = c.Query("transaction_id")
	req.Search = c.Query("search")
	req.MytaxiID = c.Query("mytaxi_id")
	req.UserID = util.ParseInt(c.Query("user_id"))
	req.UserType = c.Query("user_type")
	from := c.Query("from_date")
	if from != "" {
		req.From, _ = time.Parse("2006-01-02", from)
	}
	to := c.Query("to_date")
	if to != "" {
		req.To, _ = time.Parse("2006-01-02", to)
	}

	if req.UserID != 0 && req.UserType != "" {
		// Get account uuid
		accountBalance, err := h.repo.XpanelGetBillingBalance(c, req.UserID, req.UserType)
		if err != nil {
			return h.serviceErrorResponse(c, err.Error())
		}
		req.AccountNumber = accountBalance.AccountNumber.String
	}

	resp, err := h.repo.XpanelGetBillingPayments(c, req)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) XpanelGetBillingAccounts(c fiber.Ctx) error {
	limit := util.ParseInt(c.Query("limit", "20"))
	offset := util.ParseInt(c.Query("offset", "0"))

	userType := c.Query("user_type")

	resp, err := h.repo.XpanelGetBillingAccounts(c, userType, limit, offset)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

type refillBillingPaymentGatewayBalanceRequest struct {
	AccountNumber string      `json:"account_number"`
	Amount        float64     `json:"amount"`
	Description   null.String `json:"description"`
}

func (h *handler) XpanelRefillBillingPaymentGatewayBalance(c fiber.Ctx) error {
	var req refillBillingPaymentGatewayBalanceRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	resp, err := h.repo.XpanelRefillBillingPaymentGatewayBalance(c, req.AccountNumber, req.Amount, req.Description)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(map[string]string{"transaction_id": resp})
}

func (h *handler) XpanelGetOrderIsSuspicious(c fiber.Ctx) error {
	orderId := util.ParseInt(c.Query("order_id"))
	if orderId == 0 {
		return h.badRequestResponse(c, "incorrect order_id")
	}

	resp, err := h.repo.XpanelGetOrderIsSuspicious(c, orderId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(map[string]bool{"status": resp})
}

func (h *handler) XpanelGetClientDebts(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Query("client_id"))
	orderId := util.ParseInt(c.Query("order_id"))

	page := util.ParseInt(c.Query("page"))
	if page == 0 {
		page = 1
	}

	limit := util.ParseInt(c.Query("limit"))
	if limit == 0 {
		limit = 20
	}

	resp, err := h.repo.XpanelGetClientDebts(c, clientId, orderId, page, limit, c.Query("client_phone"))
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

type CreateDebtsXpanel struct {
	OrderId  int `json:"order_id"`
	ClientId int `json:"client_id"`
	Amount   int `json:"amount"`
}

func (h *handler) XpanelCreateClientDebt(c fiber.Ctx) (err error) {
	var req CreateDebtsXpanel

	err = c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	if req.Amount <= 0 || req.ClientId <= 0 || req.OrderId <= 0 {
		return h.badRequestResponse(c, "invalid input: amount, client_id, and order_id must be greater than zero")
	}

	err = h.repo.XpanelCreateClientDebt(c, req.OrderId, req.ClientId, req.Amount)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.SendStatus(http.StatusOK)
}

type UpdateClientDebtStatusXpanelRequest struct {
	Status int `json:"status"`
}

func (h *handler) XpanelUpdateClientDebtStatus(c fiber.Ctx) error {
	debtId := util.ParseInt(c.Params("debt_id"))
	if debtId == 0 {
		return h.badRequestResponse(c, "incorrect debt_id")
	}

	var req UpdateClientDebtStatusXpanelRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	isPaid := false
	if req.Status == 2 {
		isPaid = true
	} else if req.Status != 1 {
		return h.badRequestResponse(c, fmt.Sprintf("unknown status: %d", req.Status))
	}

	err = h.repo.XpanelUpdateClientDebtStatus(c, debtId, isPaid)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.SendStatus(http.StatusOK)
}

func (h *handler) XpanelGetAllBalance(c fiber.Ctx) error {
	resp, err := h.repo.XpanelGetAllBalance(c)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) XpanelGetProviderAccountBalance(c fiber.Ctx) error {
	providerId := util.ParseInt(c.Query("provider_id"))
	resp, err := h.app.XpanelGetProviderAccountBalance(c, providerId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(map[string]int{"balance": resp})
}

type PaySuspiciousOrderRequest struct {
	OrderId int `json:"order_id"`
}

func (h *handler) XpanelPaySuspiciousOrder(c fiber.Ctx) error {
	var req PaySuspiciousOrderRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	errType, err := h.app.XpanelPaySuspiciousOrder(c, req.OrderId)
	if err != nil {
		if errType != "" {
			return h.errorResponse(c, errType, err.Error())
		}
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.SendStatus(http.StatusOK)
}

func (h *handler) GetOrdersPaymentQueue(c fiber.Ctx) error {
	resp, err := h.app.GetOrdersPaymentQueue(c, c.Query("payment_type"))
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(map[string]any{"result": resp})
}

type cancelPaymentRequest struct {
	UserId    int `json:"user_id"`
	PaymentId int `json:"transaction_id"`
}

func (h *handler) XpanelCancelPayment(c fiber.Ctx) error {
	var req cancelPaymentRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	if req.PaymentId == 0 {
		req.PaymentId = util.ParseInt(c.Params("transaction_id"))
	}

	if req.UserId == 0 || req.PaymentId == 0 {
		return h.badRequestResponse(c, "incorrect params")
	}

	err = h.app.XpanelCancelPayment(c, req.PaymentId, req.UserId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.SendStatus(http.StatusOK)
}

func (h *handler) XpanelGetClientVisaPayments(c fiber.Ctx) (err error) {
	orderId := util.ParseInt(c.Params("order_id"))
	if orderId == 0 {
		return h.badRequestResponse(c, "incorrect order_id")
	}

	resp, err := h.repo.XpanelGetClientVisaPayments(c, orderId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) XpanelGetClientCards(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Query("client_id"))
	isDeleted := util.ParseBool(c.Query("is_deleted"))
	page := util.ParseInt(c.Query("page"))
	if page == 0 {
		page = 1
	}
	limit := util.ParseInt(c.Query("limit"))
	if limit == 0 {
		limit = 10
	}

	resp, err := h.repo.XpanelGetClientCards(c, clientId, isDeleted, c.Query("card_hash"), page, limit)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) XpanelGetDriverCards(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Query("driver_id"))
	isDeleted := util.ParseBool(c.Query("is_deleted"))
	page := util.ParseInt(c.Query("page"))
	if page == 0 {
		page = 1
	}
	limit := util.ParseInt(c.Query("limit"))
	if limit == 0 {
		limit = 10
	}

	resp, err := h.repo.XpanelGetDriverCards(c, driverId, isDeleted, page, limit)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

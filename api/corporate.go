package api

import (
	"billing_service/util"

	fiber "github.com/gofiber/fiber/v3"
)

func (h *handler) GetCorpBalance(c fiber.Ctx) error {
	corpId := util.ParseInt(c.<PERSON>ms("corp_id"))
	if corpId == 0 {
		return h.badRequestResponse(c, "incorrect corp_id")
	}

	resp, err := h.repo.GetCorpBalance(c, corpId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) CreateCorp(c fiber.Ctx) error {
	corpId := util.ParseInt(c.Params("corp_id"))
	if corpId == 0 {
		return h.badRequestResponse(c, "incorrect corp_id")
	}

	resp, err := h.repo.CreateCorp(c, corpId, c.<PERSON>("name"), c<PERSON>("reg_date"), c<PERSON>("phone"), c<PERSON>("account"), c.<PERSON>("inn"))
	if err != nil {
		return h.serviceErrorResponse(c, err.<PERSON>rror())
	}

	return c.JSON(resp)
}

func (h *handler) UpdateCorp(c fiber.Ctx) error {
	corpId := util.ParseInt(c.Params("corp_id"))
	if corpId == 0 {
		return h.badRequestResponse(c, "incorrect corp_id")
	}

	resp, err := h.repo.UpdateCorp(c, corpId, c.Params("name"), c.Params("reg_date"), c.Params("phone"), c.Params("account"), c.Params("inn"))
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) GetCorpDebt(c fiber.Ctx) error {
	userId := util.ParseInt(c.Params("user_id"))
	if userId == 0 {
		return h.badRequestResponse(c, "incorrect user_id")
	}

	if c.Query("user_type") != "corporate-company" {
		return h.badRequestResponse(c, "invalid user_type")
	}

	resp, err := h.repo.GetDebtsTotalAmount(c, userId, "corp")
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(map[string]float64{"total_debt": resp.Float64})
}

func (h *handler) CorpPay(c fiber.Ctx) (err error) {
	corpId := util.ParseInt(c.Params("corp_id"))
	amount := util.ParseInt(c.Params("amount"))
	amountType := util.ParseInt(c.Params("amount_type"))

	if corpId == 0 || amount == 0 || amountType == 0 {
		return h.badRequestResponse(c, "incorrect parameters")
	}

	err = h.app.CorpDeposit(c, corpId, amount, amountType, c.Params("description"))
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.StatusOK)
}

func (h *handler) GetOperations(c fiber.Ctx) (err error) {
	operationName := c.Params("operation_name")

	if operationName == "" {
		return h.badRequestResponse(c, "incorrect operation_name")
	}

	result, err := h.repo.GetOperations(c, operationName)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(result)
}

func (h *handler) GetParkBalance(c fiber.Ctx) error {
	parkId := util.ParseInt(c.Params("park_id"))
	if parkId == 0 {
		return h.badRequestResponse(c, "incorrect park_id")
	}

	balance, err := h.repo.GetTaxiParkBalance(c, parkId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.Map{
		"balance": balance,
	})
}

func (h *handler) GetPartnerBalance(c fiber.Ctx) error {
	partnerId := util.ParseInt(c.Params("partner_id"))
	if partnerId == 0 {
		return h.badRequestResponse(c, "incorrect partner_id")
	}

	balance, err := h.repo.GetPartnerBalance(c, partnerId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.Map{
		"balance": balance,
	})
}

func (h *handler) GetPartner(c fiber.Ctx) error {
	partnerId := util.ParseInt(c.Params("partner_id"))
	if partnerId == 0 {
		return h.badRequestResponse(c, "incorrect partner_id")
	}

	resp, err := h.repo.GetPartner(c, partnerId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

type UpdatePartnerRequest struct {
	Name    string  `json:"name"`
	INN     string  `json:"inn"`
	Percent float64 `json:"percent"`
}

func (h *handler) UpdatePartner(c fiber.Ctx) error {
	partnerId := util.ParseInt(c.Params("partner_id"))
	if partnerId == 0 {
		return h.badRequestResponse(c, "incorrect partner_id")
	}

	var req UpdatePartnerRequest
	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	err = h.repo.UpdatePartner(c, partnerId, req.Name, req.INN, req.Percent)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.StatusOK)
}

type CreatePartnerRequest struct {
	Name    string  `json:"name"`
	INN     string  `json:"inn"`
	Percent float64 `json:"percent"`
}

func (h *handler) CreatePartner(c fiber.Ctx) error {
	partnerId := util.ParseInt(c.Params("partner_id"))
	if partnerId == 0 {
		return h.badRequestResponse(c, "incorrect partner_id")
	}

	var req CreatePartnerRequest
	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	err = h.repo.CreatePartner(c, partnerId, req.Name, req.INN, req.Percent)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.StatusOK)
}

type RefillPartnerRequest struct {
	Amount      float64 `json:"amount"`
	Description string  `json:"description"`
	Date        string  `json:"date"`
	Payment     string  `json:"payment"`
}

func (h *handler) DepositPartner(c fiber.Ctx) error {
	partnerId := util.ParseInt(c.Params("partner_id"))
	if partnerId == 0 {
		return h.badRequestResponse(c, "incorrect partner_id")
	}

	var req RefillPartnerRequest
	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	err = h.repo.DepositPartner(c, partnerId, req.Amount, req.Description, req.Date, req.Payment)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.StatusOK)
}

type createParkRequest struct {
	Name      string  `json:"name"`
	RegDate   string  `json:"registration_date"`
	Phone     string  `json:"phone"`
	INN       string  `json:"inn"`
	Account   string  `json:"account"`
	PartnerId string  `json:"partner_id"`
	Percent   float64 `json:"percent"`
}

func (h *handler) CreatePark(c fiber.Ctx) error {
	parkId := util.ParseInt(c.Params("park_id"))
	if parkId == 0 {
		return h.badRequestResponse(c, "incorrect park_id")
	}

	var req createParkRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	err = h.repo.CreatePark(c, parkId, req.Name, req.RegDate, req.Phone, req.Account, req.INN, req.PartnerId, req.Percent)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.StatusOK)
}

type updateParkRequest struct {
	Name      string  `json:"name"`
	Phone     string  `json:"phone"`
	RegDate   string  `json:"registration_date"`
	INN       string  `json:"inn"`
	Account   string  `json:"account"`
	PartnerId string  `json:"partner_id"`
	Percent   float64 `json:"percent"`
}

func (h *handler) UpdatePark(c fiber.Ctx) error {
	parkId := util.ParseInt(c.Params("park_id"))
	if parkId == 0 {
		return h.badRequestResponse(c, "incorrect park_id")
	}

	var req updateParkRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	err = h.repo.UpdatePark(c, parkId, req.Name, req.Phone, req.RegDate, req.INN, req.Account, req.PartnerId, req.Percent)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.StatusOK)
}

type partnerToParkRequest struct {
	PartnerId  int `json:"partner_id"`
	TaxiParkId int `json:"taxipark_id"`
	Amount     int `json:"amount"`
}

func (h *handler) PartnerToPark(c fiber.Ctx) error {
	var req partnerToParkRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	err = h.repo.PartnerToPark(c, req.PartnerId, req.TaxiParkId, req.Amount)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.StatusOK)
}

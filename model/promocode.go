package model

import (
	"time"

	null "github.com/guregu/null/v6"
)

const (
	PromocodeStatusSuccess          = 0
	PromocodeStatusCalculationError = 51
)

var PromoCodeTypes = map[int8]string{
	1: "discount_on_distance",
	2: "discount_on_amount",
	3: "percent_discount",
	4: "voucher_discount",
}

var PromoStasuses = map[int]string{
	1:   "promocode_does_not_exist",
	2:   "promocode_is_not_active",
	4:   "status_by_stock_is_not_active",
	5:   "promocode_expired_by_time",
	7:   "limit_using_promocode_is_reached", // TODO old status: limit_promocode_exhausted
	8:   "promocode_only_for_new_users",
	9:   "limit_using_promocode_is_reached",
	10:  "promocode_attached_for_another_client",
	11:  "promocode_is_only_for_registered_customers",
	50:  "operation_will_be_reproduced_later",
	51:  "invalid_counting_amount_driver_application",
	100: "promocode_not_used_in_this_order",
}

type PromocodeInfoResponse struct {
	Success            bool   `json:"success"`
	Message            string `json:"message,omitempty"`
	CreateTime         string `json:"create_time"`
	Usage              int    `json:"usage"`
	TotalUsage         int    `json:"total_usage"`
	IsActive           bool   `json:"is_active"`
	Type               string `json:"type"`
	Value              int    `json:"value"`
	Limit              int    `json:"limit"`
	ExpireTime         string `json:"expire_time"`
	ClientId           int    `json:"client_id,omitempty"`
	CorpId             int    `json:"corp_id,omitempty"`
	CanUseTimes        int    `json:"can_use_times"`
	UsedTimes          int    `json:"used_times"`
	IsSumForOneOrder   bool   `json:"is_sum_for_one_order"`
	IsForOneClient     bool   `json:"is_for_one_client"`
	EachClientUseTimes int    `json:"each_client_use_times"`
}

type PromocodeInfo struct {
	Success            bool     `json:"success"  xml:"-"`
	Message            string   `json:"message,omitempty" xml:"-"`
	CreateTime         int64    `json:"create_time" xml:"create_time"`
	Usage              int      `json:"usage" xml:"usage"`
	TotalUsage         float64  `json:"total_usage" xml:"total_usage"`
	IsActive           bool     `json:"is_active" xml:"is_active"`
	Type               int      `json:"type" xml:"type"`
	Value              int      `json:"value" xml:"value"`
	Limit              int      `json:"limit" xml:"limit_discount"`
	ExpireTime         int64    `json:"expire_time" xml:"date_end"`
	IsForOneClient     bool     `json:"is_for_one_client" xml:"is_for_one_client"`
	ClientId           null.Int `json:"client_id" xml:"client_id"`
	CorpId             null.Int `json:"corp_id" xml:"corp_id"`
	CanUseTimes        int      `json:"can_use_times" xml:"times_use"`
	UsedTimes          int      `json:"used_times" xml:"used_times"`
	IsSumForOneOrder   bool     `json:"is_sum_for_one_order" xml:"is_sum_for_one_order"`
	IsForNewClients    bool     `json:"is_for_new_clients" xml:"is_for_new_comers"`
	EachClientUseTimes int      `json:"each_client_use_times" xml:"client_times_use"`
}

type PromocodeValidation struct {
	Success     bool   `json:"success"`
	Message     string `json:"message,omitempty"`
	PromoStatus int    `json:"promo_status"`
}

type PayPromoRequest struct {
	DriverId int    `json:"driver_id"`
	OrderId  int    `json:"order_id"`
	Amount   int    `json:"amount"`
	Comment  string `json:"comment"`
}

type PromocodeResponse struct {
	Count      int               `json:"count"`
	TotalCount int               `json:"total_count"`
	Data       []XpanelPromocode `json:"data"`
}

type XpanelPromocode struct {
	Code            string    `json:"code"`
	CreateTime      time.Time `json:"create_time,omitzero"`
	ExpireTime      null.Time `json:"expire_time,omitzero"`
	Type            string    `json:"type"`
	Value           int       `json:"value"`
	Limit           null.Int  `json:"limit,omitzero"`
	Used            null.Int  `json:"used,omitzero"`
	DiscountLimit   null.Int  `json:"discount_limit,omitzero"`
	AmountUsed      null.Int  `json:"amount_used,omitzero"`
	ClientId        null.Int  `json:"client_id,omitzero"`
	ValidDays       null.Int  `json:"valid_days,omitzero"`
	IsForNewClients bool      `json:"is_for_new_clients"`
	IsActive        bool      `json:"is_active"`
}

type Promocode struct {
	Code          string    `json:"code"`
	ExpireTime    null.Time `json:"expire_time,omitzero"`
	Type          string    `json:"type"`
	Value         int       `json:"value"`
	Limit         null.Int  `json:"limit,omitzero"`
	DiscountLimit null.Int  `json:"discount_limit,omitzero"`
	ValidDays     null.Int  `json:"valid_days,omitzero"`
}

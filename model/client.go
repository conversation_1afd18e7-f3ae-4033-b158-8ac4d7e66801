package model

import (
	"strconv"
	"time"

	"github.com/guregu/null/v6/zero"
)

type ClientInfo struct {
	IsCashbackEnabled bool      `db:"is_bonus_enabled"`
	RideCount         int       `db:"order_count"`
	RegTime           time.Time `db:"dateRegUser"`
}

// ------------------------------------------------------
// cashback

type ClientCashback struct {
	Balance     int  `json:"balance"`
	IsAvailable bool `json:"is_available"`
	IsEnabled   bool `json:"is_enabled,omitempty"`
}

// ------------------------------------------------------
// corp client

type CorpClientBalanceResponse struct {
	Response
	Available   bool `json:"available"`
	Balance     int  `json:"balance"`
	ErrorStatus int  `json:"error_status,omitempty"`
}

type CorpClientLimitResponse struct {
	Response
	Amount        int    `json:"amount,omitempty"`
	Repeatability string `json:"repeatability,omitempty"`
	IsExpireDate  bool   `json:"expire_date"`
}

type CorpClientBalance struct {
	IsCorporate      bool   `json:"is_corporate"`
	IsAvailable      bool   `json:"is_available"`
	IsManager        bool   `json:"is_manager"`
	IsCompanyBlocked bool   `json:"is_company_blocked,omitempty"`
	ErrorId          string `json:"error_id,omitempty"`
	Balance          int    `json:"balance"`
	CorpId           int    `json:"corp_id"`
}

type ClickPaymentType struct {
	PaymentTypeId int  `json:"payment_type_id"`
	IsAvailable   bool `json:"is_available"`
}

// ------------------------------------------------------
// payment types

type ClientPaymentTypesResponse struct {
	Cash       ClientCashPaymentType   `json:"cash"`
	Corp       *ClientCorpPaymentType  `json:"corp,omitempty"`
	Cards      []ClientCardPaymentType `json:"cards,omitempty"`
	Click      ClickPaymentType        `json:"click,omitzero"`
	Debts      []Debt                  `json:"debts,omitempty"`
	Cashback   ClientCashback          `json:"cashback"`
	Promocodes []Promocode             `json:"promocodes,omitempty"`
}

type ClientCashPaymentType struct {
	PaymentTypeId     int    `json:"payment_type_id"`
	IsAvailable       bool   `json:"is_available"`
	PromoAvailable    bool   `json:"promo_available,omitempty"`
	CashbackAvailable bool   `json:"cashback_available,omitempty"`
	Error             string `json:"error,omitempty"`
}

type ClientCorpPaymentType struct {
	PaymentTypeId     int    `json:"payment_type_id"`
	IsAvailable       bool   `json:"is_available"`
	PromoAvailable    bool   `json:"promo_available,omitempty"`
	CashbackAvailable bool   `json:"cashback_available,omitempty"`
	Balance           int    `json:"balance"`
	Error             string `json:"error,omitempty"`
}

type ClientCardPaymentType struct {
	PaymentTypeId     int       `json:"payment_type_id"`
	PromoAvailable    bool      `json:"promo_available,omitempty"`
	CashbackAvailable bool      `json:"cashback_available,omitempty"`
	Discount          *Discount `json:"discount,omitempty"`
	CardId            int       `json:"id"`
	Number            string    `json:"number"`
	Brand             string    `json:"brand"`
	Status            string    `json:"status"`
}

type Discount struct {
	Name  string `json:"name,omitempty"`
	Type  string `json:"type,omitempty"`
	Value int    `json:"value,omitempty"`
	Limit int    `json:"limit,omitempty"`
}

type ClientPaymentTypeOLd struct {
	Id          int    `json:"id"`
	Name        string `json:"title"`
	Alias       string `json:"alias"`
	IsAvailable bool   `json:"is_available"`
	IsVerified  bool   `json:"is_verified"`
	IsServiceOk bool   `json:"is_service_ok"`
}

// ------------------------------------------------------
// debt

type Debt struct {
	OrderId zero.Int `json:"order_id"`
	Amount  int      `json:"amount"`
	Type    string   `json:"type"`
}

type PayDebtRequest struct {
	ClientId  int         `json:"client_id"`
	CardIdRaw StringOrInt `json:"card_id"`
	CardType  string      `json:"card_type"`
}

type StringOrInt struct {
	Value int
}

func (s *StringOrInt) UnmarshalJSON(data []byte) (err error) {
	if data[0] == '"' {
		s.Value, err = strconv.Atoi(string(data[1 : len(data)-1]))
		return
	} else {
		s.Value, err = strconv.Atoi(string(data))
	}
	return
}

// ------------------------------------------------------
// tips

type PayTipsRequest struct {
	OrderId   int    `json:"order_id"`
	ClientId  int    `json:"client_id"`
	DriverId  int    `json:"driver_id"`
	CardId    int    `json:"card_id"`
	Amount    int    `json:"amount"`
	CardToken string `json:"card_token"`
}

package click

import "fmt"

type ClickError struct {
	Status  int
	Message string
}

func (e *ClickError) Error() string {
	return fmt.Sprintf("Click API error (status: %d): %s", e.Status, e.Message)
}

type ClickAPIError struct {
	Code int
	Note string
}

func (e *ClickAPIError) Error() string {
	return fmt.Sprintf("Click API error (code: %d): %s", e.Code, e.Note)
}

func NewClickError(status int, message string) error {
	return &ClickError{
		Status:  status,
		Message: message,
	}
}

func NewClickAPIError(code int, note string) error {
	return &ClickAPIError{
		Code: code,
		Note: note,
	}
}

func IsClickError(err error) bool {
	_, ok := err.(*ClickError)
	return ok
}

func IsClickAPIError(err error) bool {
	_, ok := err.(*ClickAPIError)
	return ok
}

func GetClickErrorStatus(err error) int {
	if clickErr, ok := err.(*ClickError); ok {
		return clickErr.Status
	}
	return 0
}

func GetClickErrorMessage(err error) string {
	if clickErr, ok := err.(*ClickError); ok {
		return clickErr.Message
	}
	return ""
}

func GetClickAPIErrorCode(err error) int {
	if clickErr, ok := err.(*ClickAPIError); ok {
		return clickErr.Code
	}
	return 0
}

func GetClickAPIErrorNote(err error) string {
	if clickErr, ok := err.(*ClickAPIError); ok {
		return clickErr.Note
	}
	return ""
}

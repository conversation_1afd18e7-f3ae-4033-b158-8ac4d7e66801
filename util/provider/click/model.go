package click

// HoldCreateRequest represents the request for creating a hold
type HoldCreateRequest struct {
	ServiceID   int    `json:"service_id"`
	PhoneNumber string `json:"phone_number"`
	Amount      int    `json:"amount"`
	ExternalID  string `json:"external_id"`
	Time        int    `json:"time"`
}

// HoldConfirmRequest represents the request for confirming a hold
type HoldConfirmRequest struct {
	Amount    int   `json:"amount"`
	PaymentID int64 `json:"payment_id"`
	ServiceID int   `json:"service_id"`
}

// HoldCancelRequest represents the request for canceling a hold
type HoldCancelRequest struct {
	PaymentID int64 `json:"payment_id"`
	ServiceID int   `json:"service_id"`
}

// HoldResponse represents the common response structure for hold operations
type HoldResponse struct {
	Status          int     `json:"status"`
	Message         string  `json:"message"`
	HoldID          string  `json:"hold_id"`
	PaymentID       int64   `json:"payment_id"`
	FullAmount      int     `json:"full_amount"`
	ConfirmedAmount int     `json:"confirmed_amount"`
	CancelledAmount int     `json:"cancelled_amount"`
	CreatedTime     string  `json:"created_time"`
	CompletedTime   *string `json:"completed_time"`
}

// HoldCreateResponse represents the response for creating a hold
type HoldCreateResponse HoldResponse

// HoldConfirmResponse represents the response for confirming a hold
type HoldConfirmResponse HoldResponse

// HoldCancelResponse represents the response for canceling a hold
type HoldCancelResponse HoldResponse

// HoldStatusResponse represents the response for getting hold status
type HoldStatusResponse HoldResponse

// ErrorResponse represents the error response from Click API
type ErrorResponse struct {
	ErrorCode int    `json:"error_code"`
	ErrorNote string `json:"error_note"`
}

func (r *HoldCreateResponse) IsSuccess() bool {
	return r.Status == 1
}

func (r *HoldCreateResponse) ToError() error {
	if r.IsSuccess() {
		return nil
	}
	return NewClickError(r.Status, r.Message)
}

func (r *HoldConfirmResponse) IsSuccess() bool {
	return r.Status == 2
}

func (r *HoldConfirmResponse) ToError() error {
	if r.IsSuccess() {
		return nil
	}
	return NewClickError(r.Status, r.Message)
}

func (r *HoldCancelResponse) IsSuccess() bool {
	return r.Status == 3
}

func (r *HoldCancelResponse) ToError() error {
	if r.IsSuccess() {
		return nil
	}
	return NewClickError(r.Status, r.Message)
}

func (r *HoldStatusResponse) IsHeld() bool {
	return r.Status == 1
}

func (r *HoldStatusResponse) IsConfirmed() bool {
	return r.Status == 2
}

func (r *HoldStatusResponse) IsCancelled() bool {
	return r.Status == 3
}

func (r *HoldStatusResponse) IsExpired() bool {
	return r.Status == 4
}

func (r *HoldStatusResponse) IsPending() bool {
	return r.Status == 5
}

func (r *HoldStatusResponse) IsFailed() bool {
	return r.Status == 0
}

func (r *ErrorResponse) Error() string {
	return NewClickAPIError(r.ErrorCode, r.ErrorNote).Error()
}

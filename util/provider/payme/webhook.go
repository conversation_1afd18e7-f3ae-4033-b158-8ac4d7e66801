package payme

import null "github.com/guregu/null/v6"

// request

type WebhookRequest struct {
	Id     any           `json:"id,omitempty"`
	Method string        `json:"method"`
	Params WebhookParams `json:"params"`
}

type WebhookParams struct {
	ReceiptId null.String     `json:"id,omitzero"`
	Amount    null.Int        `json:"amount,omitzero"`
	Account   *WebhookAccount `json:"account,omitempty"`
	// Time      int64                `json:"time,omitempty"`
}

type WebhookAccount struct {
	UserId        null.Int    `json:"driver_id"`
	OrderId       null.Int    `json:"order_id"`
	CardId        null.Int    `json:"card_id"`
	PaymentMethod null.String `json:"payment_method"`
}

// response

type WebhookResponse struct {
	// Jsonrpc string `json:"jsonrpc"`
	Id     any            `json:"id,omitempty"` // request id
	Result *WebhookResult `json:"result,omitempty"`
	Error  *WebhookError  `json:"error,omitempty"`
}

type WebhookResult struct {
	Allow       bool   `json:"allow,omitempty"`
	Transaction string `json:"transaction,omitempty"`
	State       int    `json:"state,omitempty"`
	CreateTime  int64  `json:"create_time,omitempty"`
	PerformTime int64  `json:"perform_time,omitempty"`
	CancelTime  int64  `json:"cancel_time,omitempty"`
	Reason      int    `json:"reason,omitempty"`
}

type WebhookError struct {
	Code    int    `json:"code"`
	Message string `json:"message,omitempty"`
	// Data    any `json:"data"`
	// Origin string `json:"origin"`
}

func (r *WebhookResponse) SetMethodNotFoundError() {
	r.Error = &WebhookError{
		Code:    -32601,
		Message: "Method not found",
	}
}

func (r *WebhookResponse) SetRPCRequestError() {
	r.Error = &WebhookError{
		Code:    -32600,
		Message: "RPC request validation error",
	}
}

func (r *WebhookResponse) SetRPCDataError() {
	r.Error = &WebhookError{
		Code:    -31050,
		Message: "RPC data validation error",
	}
}

func (r *WebhookResponse) SetAmountError() {
	r.Error = &WebhookError{
		Code:    -31001,
		Message: "Incorrect amount",
	}
}

func (r *WebhookResponse) SetAppError(text string) {
	r.Error = &WebhookError{
		Code:    -32400,
		Message: "Application error: " + text,
	}
}

func (r *WebhookResponse) SetTransactionNotFoundError() {
	r.Error = &WebhookError{
		Code:    -31003,
		Message: "Transaction not found",
	}
}

func (r *WebhookResponse) SetCannotBePerformedError() {
	r.Error = &WebhookError{
		Code:    -31008,
		Message: "Cannot be performed",
	}
}

func (r *WebhookResponse) SetAccountDataError(msg string) {
	r.Error = &WebhookError{
		Code:    -31060,
		Message: msg,
	}
}

func (r *WebhookResponse) SetPermissionDenied() {
	r.Error = &WebhookError{
		Code:    -32504,
		Message: "Permission denied",
	}
}

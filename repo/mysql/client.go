package mysql

import (
	"context"
	"database/sql"

	"billing_service/model"
	"billing_service/repo/mysql/sqls"
)

func (r *Mysql) GetClientInfo(ctx context.Context, clientId int) (resp model.ClientInfo, err error) {
	err = r.db.GetContext(ctx, &resp, sqls.GetClientInfo, clientId)
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

func (r *Mysql) GetClientPhoneById(ctx context.Context, clientId int) (phone string, err error) {
	err = r.db.GetContext(ctx, &phone, sqls.GetClientPhoneById, clientId)
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

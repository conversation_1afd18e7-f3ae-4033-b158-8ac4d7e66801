package sqls

const BlockDriver = `
UPDATE max_drivers
SET is_active = 0, block_comment = 'low_balance', blocked_date = NOW()
WHERE id = ? AND is_active = 1;`

const UnblockDriver = `
UPDATE max_drivers
SET is_active = 1, block_comment = NULL, blocked_date = NULL
WHERE id = ? AND is_active = 0 AND block_comment = 'low_balance';`

const GetDriverExists = `
SELECT EXISTS(SELECT id FROM max_drivers WHERE id = ? AND status = 1);`

const GetDriverInfo = `
SELECT id, CONCAT(driver_name, ' ', last_name) AS name, driver_phone AS phone FROM max_drivers WHERE id = ? AND status = 1;`

const DriverMoneyTransferCreate = `
INSERT INTO driver_money_transfers (sender, receiver, amount, status_id)
VALUES (?, ?, ?, 1);`

const UpdateDriverMoneyTransferTransactionStatus = `
UPDATE driver_money_transfers SET status_id = ? WHERE id = ?`

const GetDriverMoneyTransferStatus = `
SELECT status_id FROM driver_money_transfers WHERE id = ?`

const ActorActionDriverBlocked = `
INSERT INTO max_taxi_actor_actions (content_type_id, row_id, status, comments, actor_id)
VALUES (1, ?, 1, ?, 2);`

const ActorActionDriverUnblocked = `
INSERT INTO max_taxi_actor_actions (content_type_id, row_id, status, comments, actor_id)
VALUES (1, ?, 0, ?, 2);`

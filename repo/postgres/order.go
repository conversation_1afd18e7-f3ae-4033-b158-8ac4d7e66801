package postgres

import (
	"billing_service/model"
	"billing_service/repo/postgres/sqls"
	"context"

	null "github.com/guregu/null/v6"
	pgx "github.com/jackc/pgx/v5"
)

func (r *Postgres) GetClientOrderPaymentStatus(ctx context.Context, orderId int) (resp null.Int, err error) {
	err = r.db.QueryRow(ctx, sqls.GetClientOrderPaymentStatus, orderId).Scan(&resp)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

func (r *Postgres) GetDriverOrderPaymentStatus(ctx context.Context, orderId int) (resp null.Int, err error) {
	err = r.db.QueryRow(ctx, sqls.GetDriverOrderPaymentStatus, orderId).Scan(&resp)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

func (r *Postgres) CreateOrderPaymentDetails(ctx context.Context, orderId, corpId, clientId, driverId, paymentTypeId int) (err error) {
	_, err = r.db.Exec(ctx, sqls.CreateOrderPaymentDetails, orderId, corpId, clientId, driverId, paymentTypeId)
	return
}

func (r *Postgres) UpdateOrderPaymentStatusToSuccess(ctx context.Context, orderId int) (err error) {
	_, err = r.db.Exec(ctx, sqls.UpdateOrderPaymentStatusToSuccess, orderId)
	return
}

func (r *Postgres) UpdateOrderClientPaymentStatus(ctx context.Context, orderId, status int) (err error) {
	_, err = r.db.Exec(ctx, sqls.UpdateOrderClientPaymentStatus, orderId, status)
	return
}

func (r *Postgres) UpdateOrderDriverPaymentStatus(ctx context.Context, orderId, status int) (err error) {
	_, err = r.db.Exec(ctx, sqls.UpdateOrderDriverPaymentStatus, orderId, status)
	return
}

func (r *Postgres) GetOrderTips(ctx context.Context, driverId, orderId int) (resp []model.OrderTips, err error) {
	var rows pgx.Rows

	if driverId > 0 {
		rows, err = r.db.Query(ctx, sqls.GetOrderTipsByDriverId, driverId)
	} else {
		rows, err = r.db.Query(ctx, sqls.GetOrderTipsByOrderId, orderId)
	}
	if err != nil {
		return
	}
	defer rows.Close()

	for rows.Next() {
		var tip model.OrderTips
		err = rows.Scan(&tip.OrderId, &tip.Amount, &tip.CreatedAt)
		if err != nil {
			return
		}

		tip.TariffName = "Чаевые"
		tip.TariffColor = "#46BC60"

		resp = append(resp, tip)
	}

	err = rows.Err()

	return
}

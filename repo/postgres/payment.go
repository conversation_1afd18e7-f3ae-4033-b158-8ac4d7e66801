package postgres

import (
	"context"

	"billing_service/model"
	"billing_service/repo/postgres/sqls"
	"billing_service/util/provider/paynet"

	pgx "github.com/jackc/pgx/v5"
)

func (r *Postgres) CreatePayment(ctx context.Context, req model.Payment) (err error) {
	_, err = r.db.Exec(ctx, sqls.CreatePayment, req.UserId, req.UserType, req.CardId, req.OrderId, req.Amount, req.Invoice, req.ProviderId, req.Reason, req.Status, req.IsP2p)
	return
}

func (r *Postgres) UpdatePaymentStatus(ctx context.Context, invoice string, status int) (err error) {
	_, err = r.db.Exec(ctx, sqls.UpdatePaymentStatus, invoice, status)
	return
}

func (r *Postgres) GetPaymentStatusByOrderId(ctx context.Context, orderId, reason int) (resp model.Payment, err error) {
	err = r.db.QueryRow(ctx, sqls.GetPaymentStatusByOrderId, orderId, reason).Scan(&resp.Invoice, &resp.Status)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

func (r *Postgres) GetPaymentStatusByInvoice(ctx context.Context, invoice string) (resp model.Payment, err error) {
	err = r.db.QueryRow(ctx, sqls.GetPaymentStatusByInvoice, invoice).Scan(&resp.Invoice, &resp.Status)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

func (r *Postgres) GetPaymentByInvoice(ctx context.Context, invoice string) (resp model.Payment, err error) {
	err = r.db.QueryRow(ctx, sqls.GetPaymentByInvoice, invoice).Scan(
		&resp.Id, &resp.UserId, &resp.UserType, &resp.CardId, &resp.OrderId,
		&resp.Amount, &resp.Status, &resp.Reason, &resp.ProviderId, &resp.Invoice, &resp.IsP2p)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

func (r *Postgres) GetPaymentDetails(ctx context.Context, paymentId int) (resp model.PaymentDetails, err error) {
	err = r.db.QueryRow(ctx, sqls.GetPaymentDetails, paymentId).Scan(&resp.Id, &resp.OrderId, &resp.CardId, &resp.Invoice, &resp.Amount, &resp.Status, &resp.ProviderId, &resp.IsP2p, &resp.ClienId, &resp.DriverId)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

func (r *Postgres) GetPaymentDetailsByInvoice(ctx context.Context, invoice string) (resp model.PaymentDetails, err error) {
	err = r.db.QueryRow(ctx, sqls.GetPaymentDetailsByInvoice, invoice).Scan(&resp.Id, &resp.UserId, &resp.OrderId, &resp.CardId, &resp.Invoice, &resp.Amount, &resp.Status, &resp.Reason, &resp.ProviderId, &resp.IsP2p, &resp.ClienId, &resp.DriverId)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

func (r *Postgres) GetPaynetPayments(ctx context.Context, fromDate, toDate string) (resp []paynet.Statements, err error) {
	rows, err := r.db.Query(ctx, sqls.GetPaynetPayments, fromDate, toDate)
	if err != nil {
		return
	}
	defer rows.Close()

	for rows.Next() {
		var stmt paynet.Statements
		err = rows.Scan(&stmt.ProviderTrnID, &stmt.TransactionID, &stmt.Amount, &stmt.Timestamp)
		if err != nil {
			return
		}

		resp = append(resp, stmt)
	}

	err = rows.Err()

	return
}

// ------------------------------------------------------------------------------------------------------
// a2a payments

func (r *Postgres) CreatePartnerA2ATransaction(ctx context.Context, req model.A2ATransaction) (err error) {
	_, err = r.db.Exec(ctx, sqls.CreatePartnerA2ATransaction, req.OrderId, req.PartnerId, req.Amount, req.OrderAmount, req.Commission, req.ProviderType, req.Status, req.Log)
	return
}

func (r *Postgres) SetPaymentInvoiceById(ctx context.Context, id int, invoice string) (err error) {
	_, err = r.db.Exec(ctx, sqls.SetPaymentInvoiceById, invoice, id)
	return
}

func (r *Postgres) GetPaymentByOrderIdAndProvider(ctx context.Context, orderId int, providerId int8, reason int8) (resp model.Payment, err error) {
	var createdAt, updatedAt interface{} // Ignore these fields
	err = r.db.QueryRow(ctx, sqls.GetPaymentByOrderIdAndProvider, orderId, providerId, reason).Scan(
		&resp.Id, &resp.UserId, &resp.UserType, &resp.CardId, &resp.OrderId, &resp.Amount,
		&resp.Invoice, &resp.ProviderId, &resp.Reason, &resp.Status, &resp.IsP2p,
		&createdAt, &updatedAt,
	)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

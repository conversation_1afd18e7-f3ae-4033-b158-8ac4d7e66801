package order_service

import (
	"context"
	"fmt"

	"billing_service/util/http"
)

type OrderService struct {
	host   string
	client *http.Client
}

func New(host string) *OrderService {
	return &OrderService{
		host:   host,
		client: http.New(),
	}
}

type ChangePaymentTypeRequest struct {
	PaymentTypeId int8 `json:"payment_type_id"`
}

func (s *OrderService) ChangeOrderPaymentType(ctx context.Context, orderId int, paymentTypeId int8) (errType string, err error) {
	url := s.host + fmt.Sprintf("/v1/orders/%d/payment-type", orderId)

	request := ChangePaymentTypeRequest{
		PaymentTypeId: paymentTypeId,
	}

	errType, err = s.client.RequestWithoutResponse(ctx, "POST", url, request)
	if err != nil {
		err = fmt.Errorf("change order %d payment type: %v", orderId, err)
		return
	}

	return
}

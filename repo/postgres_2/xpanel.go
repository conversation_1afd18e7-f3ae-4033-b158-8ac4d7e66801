package postgres_2

import (
	"billing_service/model"
	"billing_service/util"
	"context"
	"errors"
	"fmt"

	"github.com/google/uuid"
	null "github.com/guregu/null/v6"
	pgx "github.com/jackc/pgx/v5"
)

func (r *Postgres2) XpanelGetBillingPayments(ctx context.Context, req model.XpanelGetBillingPaymentsRequest) (resp model.BillingPayments, err error) {
	resp = model.BillingPayments{
		TransactionData: make([]model.BillingPayment, 0, req.Limit),
	}

	queryCount := `
		SELECT count(*)
		FROM transactions t
		JOIN accounts a ON a.account_number = t.account_number
		JOIN mytaxi_ids id ON id.id = a.id
		JOIN mytaxi_users u ON u.mytaxi_id = id.mytaxi_id`

	query := `
		SELECT
			t.id,
			t.account_number,
			t.dst_account_number,
			t.amount,
			t.op_type,
			o.alias,
			o.description,
			t.received_amount,
			t.order_id,
			t.type,
			t.description,
			t.month,
			t.created_at
		FROM transactions t
		JOIN operations o ON o.id = t.op_type
		JOIN accounts a ON a.account_number = t.account_number
		JOIN mytaxi_ids id ON id.id = a.id
		JOIN mytaxi_users u ON u.mytaxi_id = id.mytaxi_id`

	filter := ` WHERE true = true`
	order := ` ORDER BY created_at DESC`
	offset := ` OFFSET :offset`
	limit := ` LIMIT :limit`

	params := map[string]any{}
	params["offset"] = map[bool]int{req.Page == 1: 0}[true]
	params["limit"] = map[bool]int{req.Limit != 0: req.Limit, req.Limit == 0: 10}[true]

	if req.Page > 1 {
		limit, _ := params["limit"].(int)
		params["offset"] = (req.Page - 1) * int(limit)
	}

	if len(req.Search) > 0 {
		filter += ` AND (t.description ILIKE :search || '%')`
		params["search"] = req.Search
	}

	if len(req.AccountNumber) > 0 {
		filter += ` AND t.account_number = :account_number`
		params["account_number"] = req.AccountNumber
	}

	if len(req.Type) > 0 {
		filter += ` AND t.type = :type`
		params["type"] = req.Type
	}

	if req.OpType != 0 {
		filter += ` AND t.op_type = :op_type`
		params["op_type"] = req.OpType
	}

	if len(req.OrderID) > 0 {
		filter += ` AND t.order_id = :order_id`
		params["order_id"] = req.OrderID
	}

	if req.UserID != 0 {
		filter += ` AND mytaxi_users.user_id = :user_id`
		params["user_id"] = req.UserID
	}

	if len(req.UserType) > 0 {
		filter += ` AND mytaxi_users.user_type = :user_type`
		params["user_type"] = req.UserType
	}

	if len(req.MytaxiID) > 0 {
		filter += ` AND mytaxi_users.mytaxi_id = :mytaxi_id`
		params["mytaxi_id"] = req.MytaxiID
	}

	if len(req.TransactionID) > 0 {
		filter += ` AND t.id = :transaction_id`
		params["transaction_id"] = req.TransactionID
	}

	if !req.From.IsZero() {
		filter += ` AND t.created_at >= :from`
		params["from"] = req.From
	}

	if !req.To.IsZero() {
		filter += ` AND t.created_at <= :to`
		params["to"] = req.To
	}

	q, args := util.ReplaceQueryParams(query+filter+order+offset+limit, params)

	rows, err := r.db.Query(ctx, q, args...)
	if err != nil {
		return
	}
	defer rows.Close()
	for rows.Next() {
		transactionData := model.BillingPayment{}
		err = rows.Scan(
			&transactionData.ID,
			&transactionData.AccountNumber,
			&transactionData.DestinationAccountNumber,
			&transactionData.Amount,
			&transactionData.Operation.Type,
			&transactionData.Operation.Alias,
			&transactionData.Operation.Description,
			&transactionData.ReceivedAmount,
			&transactionData.OrderID,
			&transactionData.Type,
			&transactionData.Description,
			&transactionData.Month,
			&transactionData.CreatedAt,
		)
		if err != nil {
			return
		}

		resp.TransactionData = append(resp.TransactionData, transactionData)
	}

	q, args = util.ReplaceQueryParams(queryCount+filter, params)

	err = r.db.QueryRow(ctx, q, args...).Scan(&resp.Count)

	return
}

func (r *Postgres2) XpanelGetBillingBalance(ctx context.Context, userID int, userType string) (resp model.BillingAccountBalance, err error) {
	var result struct {
		model.BillingAccountBalance
		Error string `json:"error"`
	}

	const query = "select get_account_balance($1, $2);"

	err = r.db.QueryRow(ctx, query, userType, userID).Scan(&result)
	if err != nil {
		if err == pgx.ErrNoRows {
			err = errors.New("not found")
			return
		}
		return
	}

	if result.Error != "" {
		err = fmt.Errorf("pg func: %s", result.Error)
		return
	}

	resp = result.BillingAccountBalance

	return
}

func (r *Postgres2) XpanelGetBillingAccounts(ctx context.Context, userType string, limit, offset int) (resp model.BillingAccounts, err error) {
	const (
		query      = "select account_number, a.id, description, balance from accounts as a join mytaxi_ids mi on mi.id = a.id where account_type = $1 limit $2 offset $3"
		queryCount = "select count(a.id) from accounts as a join mytaxi_ids mi on mi.id = a.id where account_type = $1;"
	)

	err = r.db.QueryRow(ctx, queryCount, userType).Scan(&resp.Total)
	if err != nil {
		if err == pgx.ErrNoRows {
			err = nil
			return
		}
		return
	}

	rows, err := r.db.Query(ctx, query, userType, limit, offset)
	if err != nil {
		return
	}
	defer rows.Close()

	for rows.Next() {
		var a model.BillingAccount
		err = rows.Scan(&a.AccountNumber, &a.ID, &a.Description, &a.Balance)
		if err != nil {
			return
		}
		resp.Accounts = append(resp.Accounts, a)
	}

	err = rows.Err()

	return
}

func (r *Postgres2) XpanelRefillBillingPaymentGatewayBalance(ctx context.Context, accNumber string, amount float64, comment null.String) (resp string, err error) {
	const query = `INSERT INTO transactions (id, account_number, amount, op_type, type, description) VALUES ($1, $2, $3, $4, $5, $6);`

	id, _ := uuid.NewRandom()
	resp = id.String()
	_, err = r.db.Exec(ctx, query, resp, accNumber, amount, OpTypePaymentGatewayDebit, TrTypeDebit, comment)

	return
}

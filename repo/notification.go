package repo

import (
	"billing_service/model"
)

func (r *Repo) SendClientNotification(clientId, orderId, amount int, msgId, msg string) {
	req := model.PushNotification{
		ClientId: clientId,
		OrderId:  orderId,
		Amount:   amount,
		Message:  msg,
	}
	err := r.nats.Publish("notifications.clients", msgId, req)
	if err != nil {
		r.log.Error("publish to nats: ", err)
	}
}

func (r *Repo) SendDriverNotification(driverId, orderId, amount int, msgId, msg string) {
	req := model.PushNotification{
		DriverId: driverId,
		OrderId:  orderId,
		Amount:   amount,
		Message:  msg,
	}
	err := r.nats.Publish("notifications.drivers", msgId, req)
	if err != nil {
		r.log.Error("publish to nats: ", err)
	}
}

func (r *Repo) SendOperatorNotification(channel, msgId, msg string) {
	req := model.TelegramNotification{
		Channel: channel,
		Message: msg,
	}
	err := r.nats.Publish("telegram.all", msgId, req)
	if err != nil {
		r.log.Error("publish to nats: ", err)
	}
}

func (r *Repo) SendSMS(phone, message string) error {
	sms := model.SMS{
		SmsNumber: phone,
		Message:   message,
	}

	return r.nats.Publish("notifications.sms", "", sms)
}

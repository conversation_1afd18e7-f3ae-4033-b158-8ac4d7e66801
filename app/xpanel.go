package app

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"billing_service/model"
	"billing_service/util/provider/atmos"
	"billing_service/util/provider/payme"

	null "github.com/guregu/null/v6"
	"github.com/riverqueue/river"
	"github.com/riverqueue/river/rivertype"
)

func (a *App) GetOrdersPaymentQueue(ctx context.Context, paymentType string) (resp []model.OrderPaymentQueue, err error) {
	var kinds []string

	switch paymentType {
	case "payme":
		kinds = []string{"pay_order_payme"}
	case "corporate":
		kinds = []string{"pay_account_to_card"}
	case "visa":
		kinds = []string{"pay_order_atmos"}
	case "tips":
		kinds = []string{"pay_tips"}
	default:
		kinds = []string{
			"pay_order_payme",       // payme
			"pay_order_atmos",       // atmos
			"pay_account_to_card",   // corp
			"pay_tips",              // tips
			"refill_driver_balance", // driver balance refill
		}
	}

	params := river.NewJobListParams().Kinds(kinds...).First(1000).States(rivertype.JobStateRetryable, rivertype.JobStateRunning, rivertype.JobStateScheduled)

	jobs, err := a.river.JobList(ctx, params)
	if err != nil {
		return
	}

	resp = make([]model.OrderPaymentQueue, len(jobs.Jobs))

	for i, job := range jobs.Jobs {
		var data model.PayOrderRequest
		err = json.Unmarshal(job.EncodedArgs, &data)
		if err != nil {
			return
		}
		amount := data.TotalClientCost
		if amount == 0 {
			amount = data.Amount
		}
		resp[i] = model.OrderPaymentQueue{
			TaskId:   job.ID,
			OrderId:  data.OrderId,
			DriverId: data.DriverId,
			Amount:   amount,
			TaskName: job.Kind,
		}
	}
	return
}

func (a *App) XpanelPaySuspiciousOrder(ctx context.Context, orderId int) (errType string, err error) {
	order, err := a.repo.GetSuspiciousOrderInfo(ctx, orderId)
	if err != nil {
		err = fmt.Errorf("get order info: %v", err)
		return
	}

	if order.Id == 0 {
		errType = "order_not_found"
		err = errors.New("order not found")
		return
	}

	if order.TotalClientCost.Int64 <= 1000 {
		errType = "low_cost_to_payment"
		err = errors.New("low cost to payment")
		return
	}

	status, err := a.repo.GetDriverOrderPaymentStatus(ctx, orderId)
	if err != nil {
		err = fmt.Errorf("get order info: %v", err)
		return
	}

	switch status.Int64 {
	case model.OrderPaymentStatusSuspiciousOrder:
		break
	case model.OrderPaymentStatusSuccess:
		errType = "payment_already_finished"
		err = errors.New("payment already finished")
		return
	default:
		err = errors.New("payment is already queued")
		return
	}

	req := model.A2CPaymentRequest{
		OrderId:  order.Id,
		DriverId: order.DriverId,
		Amount:   int(order.TotalClientCost.Int64),
		Reason:   model.PaymentReasonPayOrderForDriver,
		Comment:  fmt.Sprintf("Оплата за подозрительный заказ %d", orderId),
	}

	_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: req}, nil)

	return
}

func (a *App) XpanelGetClientPaymePayments(ctx context.Context, orderId int) (resp model.XpanelClientPaymePaymentsResponse, err error) {
	resp, err = a.repo.XpanelGetClientPaymePayments(ctx, orderId)
	for i, p := range resp.Payments {
		resp.Payments[i].PaycomStatus, err = a.payme.GetReceiptStatus(ctx, p.Invoice)
		if err != nil {
			return
		}
	}
	return
}

func (a *App) XpanelCancelPayment(ctx context.Context, paymentId, userId int) (err error) {
	payment, err := a.repo.GetPaymentDetails(ctx, paymentId)
	if err != nil {
		return
	}

	if payment.Invoice == "" {
		err = errors.New("payment not found")
		return
	}

	if payment.Amount == 0 {
		err = errors.New("total client cost not found")
		return
	}

	if payment.Status == model.PaymentStatusCancelledAfterFinish || payment.Status == model.PaymentStatusCancelled {
		return
	}

	switch payment.ProviderId {
	case model.ProviderIdPayme:
		err = a.cancelPaymePayment(ctx, payment, userId)
	case model.ProviderIdAtmos:
		err = a.cancelAtmosPayment(ctx, payment)
	default:
		err = fmt.Errorf("unknown payment provider type id: %d", payment.ProviderId)
	}

	return
}

func (a *App) cancelPaymePayment(ctx context.Context, payment model.PaymentDetails, userId int) (err error) {
	if !a.repo.GetSysParam("is_payme_enabled", "0").Bool() {
		err = errors.New("payme is not enabled")
		return
	}

	state, err := a.payme.GetReceiptStatus(ctx, payment.Invoice)
	if err != nil {
		return
	}

	switch state.Int64 {
	case payme.CheckStatusCancellationQueued, payme.CheckStatusTransactionClosingQueued:
		err = fmt.Errorf("payment in cancellation status: %v plz wait and check it later.", state.Int64)
		return

	case payme.CheckStatusCancelled:
		var changeTo int

		switch payment.Status {
		case model.PaymentStatusCreated:
			changeTo = model.PaymentStatusCancelled
		case model.PaymentStatusFinished:
			changeTo = model.PaymentStatusCancelledAfterFinish
		default:
			err = fmt.Errorf("unsupported payment status: %d", payment.Status)
			return
		}

		err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, changeTo)
		if err != nil {
			return
		}

		return

	case payme.CheckStatusPaid, payme.CheckStatusTransactionCreation:
		break

	case payme.CheckStatusPaused:
		err = errors.New("payment is paused, need to check with payme")
		return

	default:
		err = fmt.Errorf("not allowed status for cancel: %v", state.Int64)
		return
	}

	if payment.IsP2p {
		err = a.cancelPaymeP2PPayment(ctx, payment, userId)
	} else {
		err = a.cancelPaymeMerchantPayment(ctx, payment)
	}

	return
}

func (a *App) cancelPaymeMerchantPayment(ctx context.Context, payment model.PaymentDetails) (err error) {
	state, err := a.payme.CancelReceipt(ctx, payment.Invoice)
	if err != nil {
		return
	}

	if state.Int64 != payme.CheckStatusCancelled {
		if state.Int64 == payme.CheckStatusCancellationQueued {
			err = errors.New("the check is in the queue for cancellation")
		} else {
			err = fmt.Errorf("unknown payme state: %v", state.Int64)
		}
		return
	}

	var changeTo int
	switch payment.Status {
	case model.PaymentStatusCreated:
		changeTo = model.PaymentStatusCancelled
	case model.PaymentStatusFinished:
		changeTo = model.PaymentStatusCancelledAfterFinish
	}

	err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, changeTo)
	if err != nil {
		return
	}

	comment := fmt.Sprintf("Возврат денег по заказу %d", payment.OrderId.Int64)
	msgId := fmt.Sprintf("driver:%d:cancel_pay:%s", payment.UserId, payment.Invoice)

	err = a.RefineDriverBalance(ctx, payment.UserId, payment.Amount, 0, comment, msgId)

	return
}

func (a *App) cancelPaymeP2PPayment(ctx context.Context, payment model.PaymentDetails, userId int) (err error) {
	paymentForCancel, err := a.repo.GetPaymentStatusByOrderId(ctx, int(payment.OrderId.Int64), model.PaymentReasonPayOrderCancel)
	if err != nil {
		return err
	}

	if paymentForCancel.Invoice != "" {
		if paymentForCancel.Status == model.PaymentStatusFinished {
			err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusCancelledAfterFinish)
			return
		}

		var state null.Int
		state, err = a.payme.GetReceiptStatus(ctx, paymentForCancel.Invoice)
		if err != nil {
			return
		}

		switch state.Int64 {
		case payme.CheckStatusCancellationQueued, payme.CheckStatusTransactionClosingQueued:
			err = fmt.Errorf("payment in cancellation status: %v plz wait and check it later.", state.Int64)
			return

		case payme.CheckStatusCancelled:
			err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusCancelledAfterFinish)
			return

		case payme.CheckStatusPaid, payme.CheckStatusTransactionCreation:
			break

		case payme.CheckStatusPaused:
			err = errors.New("payment is paused, need to check with payme")
			return

		default:
			err = fmt.Errorf("not allowed status for cancel: %v", state.Int64)
			return
		}
	}

	clientCard, err := a.repo.GetCardById(ctx, int(payment.CardId.Int64), int(payment.ClienId.Int64), "client")
	if err != nil {
		return
	}

	_, err = a.payme.CardCheck(ctx, clientCard.PaymeToken.String)
	if err != nil {
		err = fmt.Errorf("check client card: %v", err)
		return
	}

	driverCard, err := a.repo.GetCardByUserId(ctx, int(payment.DriverId.Int64), "driver")
	if err != nil {
		return
	}

	_, err = a.payme.CardCheck(ctx, driverCard.PaymeToken.String)
	if err != nil {
		err = fmt.Errorf("check driver card: %v", err)
		return
	}

	comment := fmt.Sprintf("Возврат денег по заказу %d", payment.OrderId.Int64)

	receiptId, err := a.payme.CreateReceiptP2P(ctx, int(payment.OrderId.Int64), payment.Amount, clientCard.PaymeToken.String, comment)
	if err != nil {
		err = fmt.Errorf("failed to create cancel P2P receipt: %v", err)
		return
	}

	state, err := a.payme.PayReceipt(ctx, driverCard.PaymeToken.String, receiptId)
	if err != nil {
		err = fmt.Errorf("failed to pay receipt: %v", err)
		return
	}

	if state.Int64 != payme.CheckStatusPaid {
		err = fmt.Errorf("order %d failed to cancel p2p payment, receipt not marked as paid: %d", payment.OrderId.Int64, state.Int64)
		return
	}

	err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusCancelledAfterFinish)
	if err != nil {
		return
	}

	paymentForCancel = model.Payment{
		UserId:     userId,
		UserType:   "xpanel",
		OrderId:    payment.OrderId,
		CardId:     null.IntFrom(int64(clientCard.Id)),
		Amount:     payment.Amount,
		Status:     model.PaymentStatusFinished,
		Reason:     model.PaymentReasonPayOrderCancel,
		Invoice:    receiptId,
		ProviderId: model.ProviderIdPayme,
		IsP2p:      true,
	}

	err = a.repo.CreatePayment(ctx, paymentForCancel)
	if err != nil {
		return
	}

	comment = fmt.Sprintf("Компенсация за отмену заказа %d", payment.OrderId.Int64)

	err = a.RefillDriverBalance(ctx, int(payment.DriverId.Int64), payme.GetPaymeCommissionPrice(payment.Amount), 0, 0, comment, comment)

	return
}

func (a *App) cancelAtmosPayment(ctx context.Context, payment model.PaymentDetails) (err error) {
	if !a.repo.GetSysParam("is_atmos_enabled", "0").Bool() {
		err = errors.New("atmos is not enabled")
		return
	}

	transactionResp, err := a.atmos.GetTransaction(ctx, payment.Invoice)
	if err != nil {
		return fmt.Errorf("get transaction status: %v", err)
	}

	switch {
	case transactionResp.Payload.GetTransactionStatus() == atmos.TransactionStatusCancelled:
		var changeTo int
		switch payment.Status {
		case model.PaymentStatusCreated:
			changeTo = model.PaymentStatusCancelled
		case model.PaymentStatusFinished:
			changeTo = model.PaymentStatusCancelledAfterFinish
		default:
			err = fmt.Errorf("unsupported payment status: %d", payment.Status)
			return
		}

		err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, changeTo)
		return

	case transactionResp.Payload.IsPending():
		err = errors.New("payment is in pending status, please wait and check it later")
		return

	case transactionResp.Payload.IsFailed():
		err = fmt.Errorf("payment is in failed status: %s", transactionResp.Payload.ResultCode)
		return

	case !transactionResp.Payload.IsSuccess():
		err = fmt.Errorf("payment is in an unexpected status: %s", transactionResp.Payload.GetTransactionStatus().String())
		return
	}

	storeID := strconv.Itoa(a.cfg.Atmos.StoreID)
	cancelResp, err := a.atmos.UklonCancel(ctx, storeID, payment.Invoice)
	if err != nil {
		return fmt.Errorf("failed to cancel atmos payment: %v", err)
	}

	if !cancelResp.Status.IsSuccess() {
		return fmt.Errorf("atmos cancel request failed: %s", cancelResp.Status.Message)
	}

	var changeTo int
	switch payment.Status {
	case model.PaymentStatusCreated:
		changeTo = model.PaymentStatusCancelled
	case model.PaymentStatusFinished:
		changeTo = model.PaymentStatusCancelledAfterFinish
	default:
		err = fmt.Errorf("unsupported payment status: %d", payment.Status)
		return
	}

	err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, changeTo)
	if err != nil {
		return
	}

	comment := fmt.Sprintf("Возврат денег по заказу %d", payment.OrderId.Int64)
	msgId := fmt.Sprintf("driver:%d:cancel_pay:%s", payment.UserId, payment.Invoice)

	err = a.RefineDriverBalance(ctx, payment.UserId, payment.Amount, 0, comment, msgId)

	return
}

func (a *App) XpanelGetProviderAccountBalance(ctx context.Context, providerId int) (resp int, err error) {
	switch providerId {
	case model.ProviderIdUbk:
		resp, err = a.ubk_a2a.GetBalance(ctx, a.cfg.Ubk.AccountNumber)
		return
	default:
		err = errors.New("unknown provider_id")
		return
	}
}

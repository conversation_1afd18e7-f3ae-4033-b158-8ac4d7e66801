package app

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"time"

	"billing_service/model"
	"billing_service/util/provider/ubk_a2a"

	"github.com/google/uuid"
)

func (a *App) PrepareAndPassA2A(ctx context.Context) error {
	a.log.Infof("preparing and passing A2A at %v...", time.Now().Format("02.01.2006 15:04:05"))
	waitingPartners, err := a.repo.GetWaitingPartners(ctx, 3)
	if err != nil {
		return err
	}

	if len(waitingPartners) == 0 {
		return nil
	}

	for _, partnerId := range waitingPartners {
		partnerInfo, _ := a.repo.GetPartnerA2A(ctx, partnerId)

		partnerTransactions, err := a.repo.GetA2AToTransfer(ctx, partnerId, int(partnerInfo.DelayPeriod.Int64))
		if err != nil {
			return err
		}

		if len(partnerTransactions) == 0 {
			a.log.Infof("no pending partner transactions found for partner %d", partnerId)
			continue
		}

		switch partnerInfo.A2AAllowed.String {

		case "allow":
			var transferAmount int
			for _, transaction := range partnerTransactions {
				transferAmount += int(transaction.Amount)
			}
			if transferAmount == 0 {
				continue
			}

			partnerTransfer, err := a.repo.CreatePartnerTransferRecord(ctx, partnerId, transferAmount, uuid.New().String(), 4)
			if err != nil {
				a.log.Errorf("couldnt create partner transfer: %s", err.Error())
				return err
			}

			err = a.repo.SetTransferInfoToTransactions(ctx, partnerTransactions, partnerTransfer)
			if err != nil {
				a.log.Errorf("couldnt update partner transactions: %s", err.Error())
				continue
			}

			_, insertErr := a.river.Insert(ctx, TransferA2AArgs{TransferID: partnerTransfer.Id}, nil)
			if insertErr != nil {
				a.log.Errorf("failed to enqueue TransferA2A: %v", insertErr)
				return insertErr
			}

		case "cancel":
			_ = a.repo.BulkCancelA2A(ctx, partnerTransactions)
			msg := fmt.Sprintf("❌ A2A transaction for partner: %d is cancelled!", partnerId)
			a.repo.SendOperatorNotification("billing", fmt.Sprintf("a2a:%v:cancel", partnerId), msg)
			continue

		default:
			msg := fmt.Sprintf("⚠️ A2A transaction for partner: %d is delayed!", partnerId)
			a.repo.SendOperatorNotification("billing", fmt.Sprintf("a2a:%v:delay", partnerId), msg)
			continue
		}
	}
	return nil
}

func (a *App) TransferA2A(ctx context.Context, transferId int, attempt int) (err error) {
	const MaxAttempts = 25
	defer func() {
		if err != nil && attempt == MaxAttempts {
			_ = a.repo.ReleaseA2A(ctx, transferId, "Max retry reached")
		}
	}()

	partnerTransfer, err := a.repo.GetA2APartnerTransferRecord(ctx, transferId)
	if err != nil {
		return
	}

	partnerInfo, err := a.repo.GetPartnerA2A(ctx, partnerTransfer.PartnerId)
	if err != nil {
		return
	}

	if a.repo.GetSysParam("a2a_queue_enabled", "0").Bool() {
		switch partnerInfo.A2AAllowed.String {
		case "delay":
			_ = a.repo.DelayA2A(ctx, transferId, "Transfer delayed by sys param")
			msg := fmt.Sprintf("⚠️ A2A transaction for partner: %d is delayed from params!", partnerInfo.Id)
			a.repo.SendOperatorNotification("billing", fmt.Sprintf("a2a:%v:delay", partnerInfo.Id), msg)
			return
		case "cancel":
			_ = a.repo.CancelA2ABySysParam(ctx, transferId, "Cancelled by queue param")
			msg := fmt.Sprintf("⚠️ A2A transaction for partner: %d is cancelled from params!", partnerInfo.Id)
			a.repo.SendOperatorNotification("billing", fmt.Sprintf("a2a:%v:cancel", partnerInfo.Id), msg)
			return
		}
	} else {
		switch a.repo.GetSysParam("a2a_queue_action", "delay").Str() {
		case "cancel":
			_ = a.repo.CancelA2ABySysParam(ctx, transferId, "Cancelled by queue param")
			msg := fmt.Sprintf("❌ A2A transaction for partner: %d is cancelled from sys param!", partnerInfo.Id)
			a.repo.SendOperatorNotification("billing", fmt.Sprintf("a2a:%v:cancel", partnerInfo.Id), msg)
			return
		case "delay":
			_ = a.repo.DelayA2A(ctx, transferId, "Transfer delayed by sys param")
			msg := fmt.Sprintf("⚠️ A2A transaction for partner: %d is delayed from sys param!", partnerInfo.Id)
			a.repo.SendOperatorNotification("billing", fmt.Sprintf("a2a:%v:delay", partnerInfo.Id), msg)
			return
		}
		return
	}

	err = a.PerformTransferA2A(ctx, transferId)
	if err != nil {
		a.log.Errorf("couldnt perform transfer: %s", err.Error())
		// log failed attempt to tg
		msg := fmt.Sprintf("❌ A2A transfer failed %d", partnerTransfer.PartnerId)
		a.repo.SendOperatorNotification("billing", fmt.Sprintf("a2a:%v:failed", partnerTransfer.PartnerId), msg)
	}

	return
}

func (a *App) PerformTransferA2A(ctx context.Context, transferId int) error {
	partnerTransfer, err := a.repo.GetA2APartnerTransferRecord(ctx, transferId)
	if err != nil {
		return err
	}

	if slices.Contains([]int{ubk_a2a.StatusStarted, ubk_a2a.StatusRejected, ubk_a2a.StatusSuccess}, partnerTransfer.Status) {
		return fmt.Errorf("transfer is in progress")
	}

	partnerTransactions, err := a.repo.GetA2ATransactionByFilter(ctx, map[string]any{"transfer_id": transferId})
	if err != nil {
		return err
	}

	if len(partnerTransactions) == 0 {
		_ = a.repo.CancelA2ATransfer(ctx, transferId, "no amount to transfer")
		return nil
	}

	var finishedTransactions = make([]model.A2ATransactionView, 0)
	var pendingTransactions = make([]model.A2ATransactionView, 0)

	for _, transaction := range partnerTransactions {
		if slices.Contains([]int{32, 50, 4}, transaction.Status) {
			finishedTransactions = append(finishedTransactions, transaction)
		} else if slices.Contains([]int{30, 404, 33, 51, 444}, transaction.Status) {
			pendingTransactions = append(pendingTransactions, transaction)
		}
	}

	// update transfer id as null
	_ = a.repo.RemoveTransferIdFromTransactions(ctx, finishedTransactions)

	// pending transactions
	var transferAmount int // recalculate in any case of canceling
	for _, transaction := range pendingTransactions {
		transferAmount += int(transaction.Amount)
	}
	if transferAmount == 0 {
		_ = a.repo.ClearTransferWIthZeroAmount(ctx, transferId)
		return nil
	}
	_ = a.repo.UpdateTransferAmount(ctx, transferId, transferAmount)

	partnerInfo, err := a.repo.GetPartnerA2A(ctx, partnerTransfer.PartnerId)
	if err != nil {
		return err
	}

	createdTransaction, err := a.ubk_a2a.CreateTransaction(ctx,
		ubk_a2a.Account{
			Account:    a.ubk_a2a.Account,
			CodeFilial: a.ubk_a2a.Mfo,
			TaxID:      a.ubk_a2a.TaxId,
			Name:       "MyTaxi OPS",
		},
		ubk_a2a.Account{
			Account:    partnerInfo.Account,
			CodeFilial: partnerInfo.BankCode.String,
			TaxID:      partnerInfo.TIN,
			Name:       partnerInfo.Name,
		},
		transferAmount,
		partnerTransfer.IdempotencyKey.String,
	)

	status := 32
	createTransferDetails := map[string]any{
		"status": status,
	}
	var sleepTime int
	if err == nil {
		createTransferDetails["invoice"] = createdTransaction.Result.Transaction.TransactionId.String
		createTransferDetails["log"] = "Transfer started"
		sleepTime = 60
	} else {
		var failReason string

		switch {
		case errors.Is(err, ubk_a2a.UnauthorizedError):
			status = ubk_a2a.UnauthorizedResponseCode
			failReason = "Unauthorized"
		case errors.Is(err, ubk_a2a.TransactionNotAllowedError):
			status = ubk_a2a.StatusNotAllowed
			failReason = "Transaction not allowed"
		case errors.Is(err, ubk_a2a.InsufficientFundError):
			status = ubk_a2a.StatusInsufficientFunds
			failReason = "Insufficient funds"
		case errors.Is(err, ubk_a2a.ApiTimeoutError), errors.Is(err, ubk_a2a.NetworkError):
			status = ubk_a2a.StatusNotAllowed
			failReason = "Network error"
		case errors.Is(err, ubk_a2a.ValidationError):
			status = ubk_a2a.ValidationErrorResponseCode
			failReason = "Validation error"
			if createdTransaction.Error.GetMessage() == "External id unique" {
				err = nil
				failReason = ""
				status = ubk_a2a.StatusStarted
			}
		default:
			status = ubk_a2a.StatusUnknownError
			failReason = err.Error()
		}
		createTransferDetails["log"] = failReason
		createTransferDetails["status"] = status
		sleepTime = 30
	}
	a.repo.JointUpdateA2A(ctx, transferId, createTransferDetails)
	time.Sleep(time.Duration(sleepTime) * time.Second)

	if err != nil {
		a.log.Errorf("a2a api request not successful: %s", err.Error())
		return err
	}

	err = a.CheckA2AStatus(ctx, transferId)
	if err != nil {
		a.log.Errorf("couldnt check a2a status: %s", err.Error())
	}
	return err
}

func (a *App) CheckA2AStatus(ctx context.Context, transferId int) (err error) {
	updateDetails := map[string]any{"status": ubk_a2a.StatusStarted, "log": ""}

	partnerTransfer, err := a.repo.GetA2APartnerTransferRecord(ctx, transferId)
	if err != nil {
		return err
	}

	var transactionDetails ubk_a2a.GetTransactionResponse
	count := 1
	for count < 4 {
		transactionDetails, err = a.ubk_a2a.GetTransactionByExtId(ctx, partnerTransfer.IdempotencyKey.String)
		if err != nil {
			time.Sleep(time.Duration(count) * time.Minute)
		} else {
			break
		}

		count++
	}

	if err == nil {
		state := transactionDetails.Result.Transaction.State.String
		switch state {
		case "41":
			// StateCompleted
			updateDetails["status"] = ubk_a2a.StatusSuccess
			updateDetails["log"] = "success"
			msg := fmt.Sprintf("✅ A2A transferred to %d", partnerTransfer.PartnerId)
			a.repo.SendOperatorNotification("billing", fmt.Sprintf("a2a:%v:success", partnerTransfer.PartnerId), msg)
		case "13":
			// StateDeleted
			_ = a.repo.UpdateIdpKeys(ctx, transferId)
			err = ubk_a2a.TransactionDeleted

			msg := fmt.Sprintf("❌ A2A transfer to %d failed on check", partnerTransfer.PartnerId)
			a.repo.SendOperatorNotification("billing", fmt.Sprintf("a2a:%v:failed", partnerTransfer.PartnerId), msg)
		}
	}
	if err != nil {
		if errors.Is(err, ubk_a2a.TransactionDeleted) {
			updateDetails["status"] = ubk_a2a.StatusNotAllowed
			updateDetails["log"] = err.Error()
		} else if errors.Is(err, ubk_a2a.NotFoundError) {
			updateDetails["status"] = ubk_a2a.StatusNotFound
			updateDetails["log"] = err.Error()
		} else {
			updateDetails["status"] = ubk_a2a.StatusUnknownError
			updateDetails["log"] = err.Error()
		}
	}

	a.repo.JointUpdateA2A(ctx, transferId, updateDetails)
	return err
}

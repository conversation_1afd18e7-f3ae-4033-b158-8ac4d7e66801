package app

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"billing_service/model"

	null "github.com/guregu/null/v6"
	"github.com/riverqueue/river"
)

func (a *App) PayOrderClickTask(ctx context.Context, req model.PayOrderRequest, attempt int) (err error) {
	// Check if payment already finished
	payment, err := a.repo.GetPaymentStatusByOrderId(ctx, req.OrderId, model.PaymentReasonPayOrder)
	if err != nil {
		return fmt.Errorf("get payment status: %v", err)
	}

	if payment.IsFinished() {
		a.log.Infof("Order %d payment already finished", req.OrderId)
		return nil
	}

	// Get existing hold payment record
	holdPayment, err := a.repo.GetPaymentByOrderIdAndProvider(ctx, req.OrderId, model.ProviderIdClick, model.PaymentReasonPayOrder)
	if err != nil {
		return fmt.Errorf("get hold payment: %v", err)
	}

	if holdPayment.Id == 0 {
		return fmt.Errorf("no Click hold found for order %d", req.OrderId)
	}

	if holdPayment.Amount != req.TotalClientCost {
		a.log.Warnf("Amount mismatch for order %d: hold=%d, order=%d", req.OrderId, holdPayment.Amount, req.TotalClientCost)

		err = a.handleAmountMismatch(ctx, req, holdPayment)
		if err != nil {
			return fmt.Errorf("handle amount mismatch: %v", err)
		}

		return river.JobSnooze(30 * time.Second)
	}

	paymentID, err := strconv.ParseInt(holdPayment.Invoice, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid payment ID: %v", err)
	}

	statusResp, err := a.click.GetHoldStatusWithContext(ctx, paymentID)
	if err != nil {
		return fmt.Errorf("get hold status: %v", err)
	}

	switch {
	case statusResp.IsConfirmed():
		err = a.repo.UpdatePaymentStatus(ctx, holdPayment.Invoice, model.PaymentStatusFinished)
		if err != nil {
			return fmt.Errorf("update payment status: %v", err)
		}

		err = a.repo.UpdateOrderClientPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusSuccess)
		if err != nil {
			return fmt.Errorf("update order payment status: %v", err)
		}

		a.log.Infof("Order %d Click payment already confirmed", req.OrderId)
		return nil

	case statusResp.IsCancelled() || statusResp.IsExpired() || statusResp.IsFailed():
		a.log.Warnf("Hold failed for order %d: status=%d", req.OrderId, statusResp.Status)

		return a.orderPayCancelWithDebt(ctx, req, true)

	case statusResp.IsHeld() || statusResp.IsPending():
		confirmResp, err := a.click.ConfirmHoldWithContext(ctx, paymentID, req.TotalClientCost)
		if err != nil {
			if attempt >= 50 {
				// Max retries reached, create debt
				return a.orderPayCancelWithDebt(ctx, req, true)
			}
			return fmt.Errorf("confirm hold: %v", err)
		}

		if !confirmResp.IsSuccess() {
			if attempt >= 50 {
				// Max retries reached, create debt
				return a.orderPayCancelWithDebt(ctx, req, true)
			}
			return fmt.Errorf("hold confirmation failed: %s", confirmResp.Message)
		}

		err = a.repo.UpdatePaymentStatus(ctx, holdPayment.Invoice, model.PaymentStatusFinished)
		if err != nil {
			return fmt.Errorf("update payment status: %v", err)
		}

		err = a.repo.UpdateOrderClientPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusSuccess)
		if err != nil {
			return fmt.Errorf("update order payment status: %v", err)
		}

		a.log.Infof("Order %d Click payment confirmed successfully", req.OrderId)
		return nil

	default:
		return fmt.Errorf("unknown hold status: %d", statusResp.Status)
	}
}

func (a *App) handleAmountMismatch(ctx context.Context, req model.PayOrderRequest, oldPayment model.Payment) error {
	a.log.Infof("Amount mismatch for order %d: hold_amount=%d, order_amount=%d", req.OrderId, oldPayment.Amount, req.TotalClientCost)

	paymentID, err := strconv.ParseInt(oldPayment.Invoice, 10, 64)
	if err != nil {
		a.log.Errorf("Invalid payment ID for cancellation: %v", err)
	} else {
		_, cancelErr := a.click.CancelHoldWithContext(ctx, paymentID)
		if cancelErr != nil {
			a.log.Errorf("Failed to cancel old hold %d: %v", paymentID, cancelErr)
		} else {
			a.log.Infof("Successfully cancelled old hold %d", paymentID)
		}
	}

	err = a.repo.UpdatePaymentStatus(ctx, oldPayment.Invoice, model.PaymentStatusCancelled)
	if err != nil {
		a.log.Errorf("Failed to update old payment status: %v", err)
	}

	order, err := a.repo.GetOrderInfo(ctx, req.OrderId)
	if err != nil {
		return fmt.Errorf("get order info: %v", err)
	}

	phoneNumber, err := a.repo.GetClientPhoneById(ctx, order.ClientId)
	if err != nil {
		return fmt.Errorf("get client phone: %v", err)
	}

	externalID := fmt.Sprintf("order_%d_updated_%d", req.OrderId, time.Now().Unix())
	holdResponse, err := a.click.CreateHoldWithContext(ctx, phoneNumber, externalID, req.TotalClientCost, 60*60)
	if err != nil {
		a.log.Errorf("Failed to create new hold for order %d: %v", req.OrderId, err)
		return fmt.Errorf("create new hold: %v", err)
	}

	if !holdResponse.IsSuccess() {
		a.log.Errorf("New hold creation unsuccessful for order %d: %s", req.OrderId, holdResponse.Message)
		return fmt.Errorf("new hold creation failed: %s", holdResponse.Message)
	}

	newPayment := model.Payment{
		UserId:     order.ClientId,
		UserType:   "client",
		OrderId:    null.IntFrom(int64(req.OrderId)),
		Amount:     req.TotalClientCost, // Updated amount
		Status:     model.PaymentStatusCreated,
		Reason:     model.PaymentReasonPayOrder,
		ProviderId: model.ProviderIdClick,
		Invoice:    strconv.FormatInt(holdResponse.PaymentID, 10),
	}

	err = a.repo.CreatePayment(ctx, newPayment)
	if err != nil {
		_, cancelErr := a.click.CancelHoldWithContext(ctx, holdResponse.PaymentID)
		if cancelErr != nil {
			a.log.Errorf("Failed to cancel new hold after payment creation failure: %v", cancelErr)
		}
		return fmt.Errorf("create new payment: %v", err)
	}

	a.log.Infof("Successfully created new hold for order %d: old_amount=%d, new_amount=%d, hold_id=%s, payment_id=%d",
		req.OrderId, oldPayment.Amount, req.TotalClientCost, holdResponse.HoldID, holdResponse.PaymentID)

	return nil
}

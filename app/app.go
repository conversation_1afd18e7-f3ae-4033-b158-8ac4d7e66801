package app

import (
	"context"
	"log/slog"
	"sync"
	"time"

	"billing_service/repo"
	"billing_service/util"
	"billing_service/util/config"
	"billing_service/util/fernet"
	"billing_service/util/logger"
	"billing_service/util/nats"
	"billing_service/util/provider/atmos"
	"billing_service/util/provider/click"
	"billing_service/util/provider/payme"
	"billing_service/util/provider/ubk"
	"billing_service/util/provider/ubk_a2a"
	"billing_service/util/redis"

	"github.com/bytedance/sonic"
	pgx "github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	redis_client "github.com/redis/go-redis/v9"
	"github.com/riverqueue/river"
	"github.com/riverqueue/river/riverdriver/riverpgxv5"
	"github.com/riverqueue/river/rivermigrate"
	"github.com/riverqueue/river/rivershared/util/slogutil"
	cron "github.com/robfig/cron/v3"
)

var (
	json = sonic.ConfigDefault
	app  *App
	once sync.Once
)

type App struct {
	ctx       context.Context
	cfg       *config.Config
	log       *logger.Logger
	repo      *repo.Repo
	redis     *redis_client.Client
	nats      *nats.Client
	payme     *payme.Client
	atmos     *atmos.Client
	ubk       *ubk.Client
	ubk_a2a   *ubk_a2a.Client
	fernetKey *fernet.Key
	river     *river.Client[pgx.Tx]
	click     *click.Client
	// cache  otter.CacheWithVariableTTL[string, any]
}

func Get(ctx context.Context) *App {
	once.Do(func() {
		cfg := config.Get()
		log := logger.Get(cfg.LogLevel)
		repo := repo.Get(ctx, cfg, log)

		fernetKey, err := fernet.DecodeKey(cfg.FernetKey)
		if err != nil {
			panic(err)
		}

		app = &App{
			ctx:       ctx,
			cfg:       cfg,
			log:       log,
			repo:      repo,
			fernetKey: fernetKey,
			redis:     redis.Get(cfg.Redis.Host, cfg.Redis.Password, cfg.Redis.DB),
			nats:      nats.Get(cfg.Nats.Host, cfg.Nats.Token),
			payme:     payme.NewClient(cfg.Payme.Url, cfg.Payme.Id, cfg.Payme.Key),
			atmos:     atmos.NewClient(cfg.Atmos.Url, cfg.Atmos.ClientID, cfg.Atmos.ClientSecret, cfg.Atmos.StoreID),
			ubk:       ubk.NewClient(cfg.Ubk.Url, cfg.Ubk.AccountNumber, cfg.Ubk.Username, cfg.Ubk.Password, cfg.Ubk.SecretKey),
			ubk_a2a:   ubk_a2a.NewClient(cfg.UbkA2A.Url, cfg.UbkA2A.Username, cfg.UbkA2A.Password, cfg.UbkA2A.Account, cfg.UbkA2A.TaxId),
			click:     click.NewClient(cfg.Click.MerchantID, cfg.Click.ServiceID, cfg.Click.SecretKey, cfg.Click.MerchantUserId),
		}

		err = app.startRiver()
		if err != nil {
			panic(err)
		}

		// cache, err := otter.MustBuilder[string, any](maxDrivers).WithVariableTTL().Build()
		// if err != nil {
		// 	panic(err)
		// }
		// app.cache = cache
	})

	return app
}

func (a *App) startRiver() (err error) {
	db, err := pgxpool.New(a.ctx, a.cfg.PaymentPostgres.GetDSN())
	if err != nil {
		return
	}

	err = db.Ping(a.ctx)
	if err != nil {
		return
	}

	riverDb := riverpgxv5.New(db)

	migrator, err := rivermigrate.New(riverDb, nil)
	if err != nil {
		return
	}

	// Migrate to version 5. An actual call may want to omit all MigrateOpts,
	// which will default to applying all available up migrations.
	_, err = migrator.Migrate(a.ctx, rivermigrate.DirectionUp, nil)
	if err != nil {
		return
	}

	workers := river.NewWorkers()
	river.AddWorker(workers, &PayOrderPaymeWorker{app: app})
	river.AddWorker(workers, &PayOrderAtmosWorker{app: app})
	river.AddWorker(workers, &PayOrderClickWorker{app: app})
	river.AddWorker(workers, &PayA2CWorker{app: app})
	river.AddWorker(workers, &PayTipsWorker{app: app})
	river.AddWorker(workers, &PayTipsAtmosWorker{app: app})
	river.AddWorker(workers, &RefillDriverBalanceWorker{app: app})
	river.AddWorker(workers, &GetAndProcessA2AWorker{app: app})
	river.AddWorker(workers, &TransferA2AWorker{app: app})
	river.AddWorker(workers, &ResetCardPaymentAttemptsWorker{app: app})

	periodicJobs := a.createPeriodicJobs()
	a.river, err = river.NewClient(riverDb, &river.Config{
		JobTimeout:  5 * time.Minute,
		RetryPolicy: &util.RiverCustomRetryPolicy{},
		Logger:      slog.New(&slogutil.SlogMessageOnlyHandler{Level: slog.LevelError}),
		Queues: map[string]river.QueueConfig{
			river.QueueDefault: {MaxWorkers: 1000},
		},
		Workers:      workers,
		PeriodicJobs: periodicJobs,
	})
	if err != nil {
		return
	}

	err = app.river.Start(a.ctx)

	return
}

func (a *App) Shutdown(ctx context.Context) {
	if a.river != nil {
		err := a.river.Stop(ctx)
		if err != nil {
			a.log.Error(err.Error())
		}
	}

	if a.repo != nil {
		err := a.repo.Close()
		if err != nil {
			a.log.Error(err.Error())
		}
	}

	if a.redis != nil {
		err := a.redis.Close()
		if err != nil {
			a.log.Error(err.Error())
		}
	}
}

func (a *App) createPeriodicJobs() []*river.PeriodicJob {
	periodicJobs := make([]*river.PeriodicJob, 0, 3)

	jobConfigs := []struct {
		schedule string
		jobFn    func() (river.JobArgs, *river.InsertOpts)
	}{
		{
			schedule: "0 12 3-31/3 * *",
			jobFn: func() (river.JobArgs, *river.InsertOpts) {
				return GetAndProcessA2AArgs{}, nil
			},
		},
		{
			// Run at 00:00 on the 1st day of each month
			schedule: "0 0 1 * *",
			jobFn: func() (river.JobArgs, *river.InsertOpts) {
				return ResetCardPaymentAttemptsArgs{}, nil
			},
		},
	}

	for _, config := range jobConfigs {
		schedule, err := cron.ParseStandard(config.schedule)
		if err != nil {
			a.log.Errorf("invalid cron schedule: %v", err)
			continue
		}

		job := river.NewPeriodicJob(schedule, config.jobFn, nil)
		periodicJobs = append(periodicJobs, job)
	}

	return periodicJobs
}

package app

import (
	"context"
	"fmt"
	"strconv"

	"billing_service/model"

	"github.com/google/uuid"
	null "github.com/guregu/null/v6"
)

func (a *App) CreateClickHold(ctx context.Context, req model.CreateClickHoldRequest) (err error) {
	order, err := a.repo.GetOrderInfo(ctx, req.OrderId)
	if err != nil {
		err = fmt.Errorf("get order info: %v", err)
		return
	}

	if order.ClientId == 0 {
		err = fmt.Errorf("order not found")
		return
	}

	phoneNumber, err := a.repo.GetClientPhoneById(ctx, order.ClientId)
	if err != nil {
		err = fmt.Errorf("get client phone: %v", err)
		return
	}

	if phoneNumber == "" {
		err = fmt.Errorf("client phone number not found")
		return
	}

	// Use total_client_cost as amount
	if !order.TotalClientCost.Valid {
		err = fmt.Errorf("order total cost not available")
		return
	}

	amount := int(order.TotalClientCost.Int64)
	if amount <= 0 {
		err = fmt.Errorf("invalid order amount: %d", amount)
		return
	}

	// Static 60 minutes hold time
	timeMinutes := 60

	externalID := fmt.Sprintf("order_%d_%s", req.OrderId, uuid.NewString()[:8])

	holdResponse, err := a.click.CreateHoldWithContext(ctx, phoneNumber, externalID, amount, timeMinutes*60)
	if err != nil {
		a.log.Errorf("Click hold creation failed for order %d: %v", req.OrderId, err)
		return a.fallbackToCashPayment(ctx, req.OrderId, "click hold creation failed")
	}

	if !holdResponse.IsSuccess() {
		a.log.Errorf("Click hold creation unsuccessful for order %d: status %d, message: %s",
			req.OrderId, holdResponse.Status, holdResponse.Message)
		return a.fallbackToCashPayment(ctx, req.OrderId, "click hold creation unsuccessful")
	}

	payment := model.Payment{
		UserId:     order.ClientId,
		UserType:   "client",
		OrderId:    null.IntFrom(int64(req.OrderId)),
		Amount:     amount,
		Status:     model.PaymentStatusCreated,
		Reason:     model.PaymentReasonPayOrder,
		ProviderId: model.ProviderIdClick,
		Invoice:    strconv.FormatInt(holdResponse.PaymentID, 10),
	}

	err = a.repo.CreatePayment(ctx, payment)
	if err != nil {
		a.log.Errorf("Failed to create payment record for order %d: %v", req.OrderId, err)

		_, cancelErr := a.click.CancelHoldWithContext(ctx, holdResponse.PaymentID)
		if cancelErr != nil {
			a.log.Errorf("Failed to cancel hold %d after payment creation failure: %v", holdResponse.PaymentID, cancelErr)
		}

		return fmt.Errorf("failed to create payment record: %v", err)
	}

	_, err = a.repo.GetPaymentByInvoice(ctx, payment.Invoice)
	if err != nil {
		a.log.Errorf("Failed to get created payment for order %d: %v", req.OrderId, err)
	}

	a.log.Infof("Click hold created successfully for order %d: hold_id=%s, payment_id=%d",
		req.OrderId, holdResponse.HoldID, holdResponse.PaymentID)

	return
}

func (a *App) fallbackToCashPayment(ctx context.Context, orderId int, reason string) (err error) {
	a.log.Warnf("Falling back to cash payment for order %d: %s", orderId, reason)

	_, err = a.repo.ChangeOrderPaymentType(ctx, orderId, model.PaymentTypeCash)
	if err != nil {
		return fmt.Errorf("change order payment type to cash: %v", err)
	}

	a.log.Infof("Order %d payment type changed to cash due to Click payment failure", orderId)
	return
}

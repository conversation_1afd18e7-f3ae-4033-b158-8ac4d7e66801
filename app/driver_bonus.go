package app

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"billing_service/model"
)

func (a *App) ProcessDriverBonus(ctx context.Context, req model.DriverBonusEvent) (err error) {
	comment := fmt.Sprintf("Driver bonus payment for challenge %d", req.ChallengeId)

	a2cRequest := model.A2CPaymentRequest{
		OrderId:  req.ChallengeId,
		DriverId: req.DriverId,
		Amount:   req.BonusAmount,
		Reason:   model.PaymentReasonDriverBonus,
		Comment:  comment,
	}

	_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: a2cRequest}, nil)

	return
}

func (a *App) publishDriverBonusFiscalization(req model.A2CPaymentRequest) error {
	challengeId := strconv.Itoa(req.OrderId)
	receipt := model.FiscalizationBonusReceipt{
		ReceiptId: challengeId,
		DriverId:  req.DriverId,
		Price:     req.Amount,
		Datetime:  time.Now(),
	}

	msgId := fmt.Sprintf("driver_bonus_fiscalization:%d:%s", req.DriverId, challengeId)
	err := a.nats.Publish("orders.fiscalization.bonus", msgId, receipt)
	if err != nil {
		return fmt.Errorf("failed to publish to orders.fiscalization.bonus: %v", err)
	}

	return nil
}

package app

import (
	"context"
	"errors"
	"fmt"

	"billing_service/model"
	"billing_service/util/fernet"
	"billing_service/util/provider/payme"

	null "github.com/guregu/null/v6"
)

func (a *App) GetDriverCard(ctx context.Context, driverId int) (resp *model.Card, err error) {
	card, err := a.repo.GetCardByUserId(ctx, driverId, "driver")
	if err != nil {
		return
	}

	if card.Id == 0 {
		return
	}

	card.Exists = true

	if a.repo.GetSysParam("is_payme_enabled", "0").Bool() {
		if card.PaymeToken.String == "" {
			card.Status = "error"
		} else {
			check, err := a.payme.CardCheck(ctx, card.PaymeToken.String)
			if err != nil {
				switch err {
				case payme.ErrCardNotFound,
					payme.ErrCardExpired,
					payme.ErrInvalidTokenFormat:
					card.Status = "error"
				default:
					card.Status = "fail"
				}
			} else if check.Result.Card.Verify {
				card.Status = "success"
				card.Exists = true
			} else {
				card.Status = "fail"
			}
		}
	} else {
		card.Status = "fail"
	}

	resp = &card

	return
}

func (a *App) AddDriverCard(ctx context.Context, driverId int, token, fullNumber string) (errType string, err error) {
	check, err := a.payme.CardCheck(ctx, token)
	if err != nil {
		switch err {
		case payme.ErrCardNotFound, payme.ErrInvalidTokenFormat:
			errType = "card_is_not_valid"
		case payme.ErrCardExpired:
			errType = "card_is_expired"
		default:
			errType = "processing_error"
		}
		return
	} else if !check.Result.Card.Verify {
		errType = "card_is_not_valid"
		err = errors.New("card is not valid")
		return
	}

	if fullNumber != "" {
		enc, e := fernet.EncryptAndSign([]byte(fullNumber), app.fernetKey)
		if e != nil {
			err = fmt.Errorf("encrypt card full number: %v", e)
			return
		}
		fullNumber = string(enc)
	}

	card := model.Card{
		PaymeToken: null.StringFrom(token),
		Number:     check.Result.Card.Number,
		Expire:     null.StringFrom(check.Result.Card.Expire),
		FullNumber: null.StringFrom(fullNumber),
	}

	if check.Result.Card.NumberHash != "" {
		card.PaymeHash = null.StringFrom(check.Result.Card.NumberHash)
	}

	card.Brand, card.Category, err = a.getCardBrand(ctx, card.Number)
	if err != nil {
		return
	}

	err = a.repo.AddPaymeCard(ctx, driverId, "driver", card)

	return
}

func (a *App) DeleteDriverCard(ctx context.Context, driverId int) (err error) {
	card, err := a.repo.GetCardByUserId(ctx, driverId, "driver")
	if err != nil {
		return
	}

	if card.Id == 0 {
		return
	}

	success, err := a.payme.CardRemove(ctx, card.PaymeToken.String)
	if err != nil {
		switch err {
		case payme.ErrCardNotFound,
			payme.ErrInvalidTokenFormat:
			err = a.repo.DeleteCard(ctx, card.Id, driverId, "driver")
		}
		return
	} else if success {
		err = a.repo.DeleteCard(ctx, card.Id, driverId, "driver")
	}

	return
}

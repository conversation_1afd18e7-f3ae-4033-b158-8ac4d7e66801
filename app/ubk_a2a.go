package app

import (
	"bytes"
	"context"
	"fmt"
	"time"

	"billing_service/model"

	null "github.com/guregu/null/v6"
	excelize "github.com/xuri/excelize/v2"
)

func (a *App) GetA2ATransactionForOrder(ctx context.Context, orderId int) (resp []model.A2ATransactionView, err error) {
	resp, err = a.repo.GetA2AByOrderId(ctx, orderId)
	return
}

func (a *App) GetA2ABalance(ctx context.Context) (resp int, err error) {
	resp, err = a.ubk_a2a.GetBalance(ctx, "")
	return
}

func (a *App) CancelA2ATransaction(ctx context.Context, transactionId int, comment null.String) error {
	a2aRecords, err := a.repo.Postgres.GetA2AByTransactionId(ctx, transactionId)
	if err != nil {
		return err
	}

	if len(a2aRecords) == 0 {
		return fmt.Errorf("partner transfer does not exist")
	}

	a2aRecord := a2aRecords[0]

	if a2aRecord.Status == 30 || a2aRecord.Status == 5 {
		return a.repo.CancelA2ATransaction(ctx, transactionId, comment)
	}

	return fmt.Errorf("partner transfer already finished")
}

func parseDateParam(param string) (*time.Time, error) {
	if param == "" {
		return nil, nil
	}

	parsedDate, err := time.Parse("02.01.2006", param)
	if err != nil {
		return nil, fmt.Errorf("invalid date format for %s, expected dd.mm.yyyy", param)
	}

	fixedZone := time.FixedZone("+05:00", 5*60*60)
	tzAgnosticDate := time.Date(
		parsedDate.Year(),
		parsedDate.Month(),
		parsedDate.Day(),
		parsedDate.Hour(),
		parsedDate.Minute(),
		parsedDate.Second(),
		parsedDate.Nanosecond(),
		fixedZone,
	)
	return &tzAgnosticDate, nil
}

func (a *App) GetA2aTransactions(ctx context.Context, filterParams map[string]any, download bool) (model.A2AList, error) {
	var page, pageSize int

	if fromDate, ok := filterParams["from_date"]; ok && fromDate != "" {
		if strFromDate, ok := fromDate.(string); ok {
			parsed, _ := parseDateParam(strFromDate)
			filterParams["from_date"] = parsed
		}
	}
	if toDate, ok := filterParams["to_date"]; ok && toDate != "" {
		if strToDate, ok := toDate.(string); ok {
			parsed, _ := parseDateParam(strToDate)
			*parsed = parsed.Add(time.Hour*23 + time.Minute*59 + time.Second*59)
			filterParams["to_date"] = parsed
		}
	}
	page = filterParams["page"].(int)
	pageSize = filterParams["page_size"].(int)

	delete(filterParams, "page")
	delete(filterParams, "page_size")

	var resp model.A2AList
	if download {
		pageSize = 0
	}
	var offset = (page - 1) * pageSize
	transactions, count, err := a.repo.FilterA2AForListView(ctx, filterParams, pageSize, offset)
	if err != nil {
		return resp, err
	}

	resp = model.A2AList{
		Count:   count,
		Results: transactions,
	}
	return resp, nil
}

func (a *App) UpdateA2ATransaction(ctx context.Context, transactionId, amount int, comment null.String) error {
	a2aRecords, err := a.repo.GetA2AByTransactionId(ctx, transactionId)
	if err != nil {
		return err
	}

	if len(a2aRecords) == 0 {
		return fmt.Errorf("partner transfer does not exist")
	}

	if int64(amount) >= int64(a2aRecords[0].Amount) {
		return fmt.Errorf("new amount value should be lower than current amount")
	}

	return a.repo.UpdateA2AAmount(ctx, transactionId, amount, comment)
}

var StatusMap = map[int]string{
	30:  "Запланировано",
	4:   "Успешно",
	5:   "Оплатить вручную",
	50:  "Отклонено",
	32:  "Транзакция начата",
	33:  "Транзакция не разрешена",
	404: "Транзакция не найдена",
	401: "Не авторизовано",
}

func (a *App) CreatePartnerPaymentsReport(payments []model.A2ATransactionView) (buf *bytes.Buffer, err error) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			a.log.Error("Error closing Excel file: ", err)
		}
	}()

	partnerIdsMap, err := a.repo.GetAllPartners(context.Background())
	if err != nil {
		return
	}

	sheet := "Sheet1"
	_, err = f.NewSheet(sheet)
	if err != nil {
		return
	}

	headers := []string{"ID", "Время создания", "ID Заказа", "Сумма", "Инвойс", "Статус", "Комментарии", "Партнер"}
	for col, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+col)
		_ = f.SetCellValue(sheet, cell, header)
	}

	for row, payment := range payments {
		baseRow := row + 2
		_ = f.SetCellValue(sheet, fmt.Sprintf("A%d", baseRow), payment.Id)
		_ = f.SetCellValue(sheet, fmt.Sprintf("B%d", baseRow), payment.CreatedAt.Format("2006-01-02 15:04:05"))
		_ = f.SetCellValue(sheet, fmt.Sprintf("C%d", baseRow), payment.OrderId)
		_ = f.SetCellValue(sheet, fmt.Sprintf("D%d", baseRow), payment.Amount)
		_ = f.SetCellValue(sheet, fmt.Sprintf("E%d", baseRow), payment.Invoice.String)
		_ = f.SetCellValue(sheet, fmt.Sprintf("F%d", baseRow), StatusMap[payment.Status])
		_ = f.SetCellValue(sheet, fmt.Sprintf("G%d", baseRow), payment.Comment.String)
		_ = f.SetCellValue(sheet, fmt.Sprintf("H%d", baseRow), partnerIdsMap[payment.PartnerId])
	}

	buf = new(bytes.Buffer)
	err = f.Write(buf)

	return
}
